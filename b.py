import json
import requests
import logging
import os
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Any
import time
import re  # 引入 re 模块用于正则表达式匹配
from loguru import logger


class PerAiChatQueryRequest:
    def __init__(self):
        self.system_role: Optional[str] = None
        self.model_type: str = ""
        self.model_id: str = ""
        self.api_key: Optional[str] = None
        self.credentials: Optional[str] = None
        self.location: Optional[str] = None
        self.anthropic_version: str = ""
        self.model_version: str = ""
        self.output_sku: str = ""
        self.input_sku: str = ""
        self.region: str = ""
        self.ak: str = ""
        self.sk: str = ""
        self.project_id: Optional[str] = None
        self.prompt: str = ""
        self.company_id: Optional[str] = None
        self.chat_history: Optional[List[Dict]] = None


def per_ai_chat_query(per_ai_chat_query_request: PerAiChatQueryRequest, llm_path: str) -> Optional[str]:
    """
    AI聊天查询方法
    """
    conf = {
        "temperature": 0.1,
        "max_tokens": 2048,
        "stop": "</response>",
        "top_p": 0.2,
        "system_role": per_ai_chat_query_request.system_role if per_ai_chat_query_request.system_role else ""
    }

    param = {
        "knn_faq_threshold": "1.95",
        "knn_paragraph_threshold": "1.55",
        "knn_sentence_threshold": "1.4",
        "timeout": "300",
        "model_id": "us.anthropic.claude-3-5-haiku-20241022-v1:0",
        "api_key": per_ai_chat_query_request.api_key,
        "knn_intent_threshold": "1.6",
        "credentials": per_ai_chat_query_request.credentials,
        "location": per_ai_chat_query_request.location,
        "anthropic_version": "bedrock-2023-05-31",
        "model_version": "bedrock-runtime",
        "output_sku": "0995b818-3341-4e9c-aa88-d243ec191156",
        "input_sku": "ced1dfba-8ab9-4a16-96cd-4aace51f2ffd",
        "region": "us-east-1",
        "ak": "********************",
        "sk": "+fX8RRLdpGlPkaEX03ghRSPDje7K49CWZc6unLFN",
        "project_id": per_ai_chat_query_request.project_id
    }

    json_data = {
        "llm_model_type": "bedrock-claude-3-5-haiku",
        "llm_model_param": param,
        "prompt_conf": conf,
        "prompt": per_ai_chat_query_request.prompt,
        "use_stream": False,
        "company_id": per_ai_chat_query_request.company_id,
        "origin_prompt": per_ai_chat_query_request.prompt,
        "func_name": "All-Know-Content-Review",
        "extend_attr": {}
    }

    if per_ai_chat_query_request.chat_history:
        json_data["chat_history"] = per_ai_chat_query_request.chat_history

    logger.info(f"请求路径：{llm_path}")

    try:
        response = requests.post(
            url=f"{llm_path}/llm",
            json=json_data,
            headers={"Content-Type": "application/json"},
            timeout=100
        )

        llm_response = response.text
        logger.info(f"AI模型返回状态码：{response.status_code}")

        if response.status_code == 200:
            return llm_response
        else:
            logger.error(f"请求失败，状态码：{response.status_code}，响应：{llm_response}")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常：{e}")
        return None
    except Exception as e:
        logger.error(f"未知异常：{e}")
        return None


def translate_text_with_retry(text: str, llm_path: str, company_id: str, max_retries: int = 5) -> Optional[str]:
    """
    带重试机制的翻译文本函数

    Args:
        text: 要翻译的英文文本
        llm_path: AI模型服务路径
        company_id: 公司ID
        max_retries: 最大重试次数

    Returns:
        翻译后的印尼语文本，失败返回None
    """
    for attempt in range(max_retries + 1):  # +1 因为第一次不算重试
        if attempt > 0:
            logger.info(f"第 {attempt} 次重试翻译...")
            time.sleep(2)  # 重试前等待2秒

        result = translate_text(text, llm_path, company_id)
        result = result.replace('</response>','').replace('<response>','').replace('\\','')

        if result:
            return result

        if attempt < max_retries:
            logger.warning(f"翻译失败，将进行第 {attempt + 1} 次重试")

    logger.error(f"翻译失败，已重试 {max_retries} 次")
    return None


def translate_text(text: str, llm_path: str, company_id: str = "translation_company") -> Optional[str]:
    """
    翻译文本到印尼语

    Args:
        text: 要翻译的英文文本
        llm_path: AI模型服务路径
        company_id: 公司ID

    Returns:
        翻译后的印尼语文本
    """
    request = PerAiChatQueryRequest()
    request.system_role = """You are a professional translation assistant. Please accurately translate the English text provided by the user into Bahasa Indonesia.
Requirements:
1. Ensure that the translation is accurate, natural, and in line with Indonesian habits
2. If the original text contains special formats (such as JSON, XML, code, etc.), please keep the format unchanged and only translate the text content
3. There is no need to check whether the input text is complete, only output the translation result. If the input text is incomplete, only output the translation result of the existing text
4. Directly return the translation result without adding any explanation or description
5. Keep HTML tags like <a>, <b>, <span> etc. unchanged - only translate the text content inside them
6. Keep backticks (`) unchanged - do not convert them to single quotes
7. Preserve all original formatting and special characters
8. IMPORTANT: Only translate the VALUE part of key-value pairs, never translate the KEY names
9. Place the translation result in the<response>tag in the format:<response>Translation result</response>

For example:
Input: Input text: Nice to meet you
Output:<response>Senang bertemu denganmu.</response>
"""

    request.prompt = f"Input：{text}"
    request.company_id = company_id

    result = per_ai_chat_query(request, llm_path)

    if result:
        try:
            # 尝试解析JSON响应
            response_data = json.loads(result)
            if isinstance(response_data, dict) and 'answer' in response_data:
                return response_data['answer'].strip().strip('"').strip("'")

        except json.JSONDecodeError:
            # 如果不是JSON格式，直接返回文本
            return result

    return None


def get_supported_file_extensions():
    """
    获取支持的文件扩展名 (已添加 .js)
    """
    return {'.txt', '.json', '.xml', '.html', '.htm', '.md', '.csv', '.properties', '.yml', '.yaml', '.js'}


def parse_js_object_advanced(js_content: str) -> Optional[Dict[str, str]]:
    """
    使用正则表达式解析JavaScript对象，支持反引号、HTML标签和各种引号
    只提取value部分用于翻译，key保持不变

    Args:
        js_content: JavaScript对象内容字符串

    Returns:
        解析后的字典，失败返回None
    """
    try:
        # 移除注释
        content_no_comments = re.sub(r'//.*', '', js_content)

        # 匹配键值对，支持单引号、双引号、反引号
        # 这个正则表达式可以匹配：
        # 'key': 'value'
        # "key": "value"
        # `key`: `value`
        # 以及各种组合，并且支持值中包含HTML标签
        pattern = r'''
            (['\"`])([^'\"`]+?)\1\s*:\s*  # 匹配键（支持单引号、双引号、反引号）
            (['\"`])((?:[^\\]|\\.)*)?\3   # 匹配值（支持转义字符和HTML标签）
        '''

        matches = re.findall(pattern, content_no_comments, re.VERBOSE | re.DOTALL)

        if not matches:
            logger.warning("未找到任何键值对")
            return None

        result = {}
        for match in matches:
            key = match[1]  # 键 - 保持原样，不翻译
            value = match[3]  # 值 - 这是需要翻译的部分

            # 处理转义字符
            value = value.replace("\\'", "'").replace('\\"', '"').replace("\\`", "`")

            result[key] = value

        logger.info(f"成功解析 {len(result)} 个键值对")
        return result

    except Exception as e:
        logger.error(f"解析JavaScript对象时出错: {e}")
        return None


def escape_js_string(value: str, quote_char: str = "'") -> str:
    """
    转义JavaScript字符串中的特殊字符

    Args:
        value: 要转义的字符串
        quote_char: 使用的引号字符

    Returns:
        转义后的字符串
    """
    # 转义反斜杠
    value = value.replace("\\", "\\\\")

    # 根据引号类型进行转义
    if quote_char == "'":
        value = value.replace("'", "\\'")
    elif quote_char == '"':
        value = value.replace('"', '\\"')
    elif quote_char == "`":
        value = value.replace("`", "\\`")

    # 转义换行符
    value = value.replace("\n", "\\n").replace("\r", "\\r")

    return value


def rebuild_js_content(original_content: str, translated_dict: Dict[str, str]) -> str:
    """
    重新构建JavaScript文件内容，保持原有的引号风格和格式

    Args:
        original_content: 原始JavaScript内容
        translated_dict: 翻译后的键值对字典

    Returns:
        重新构建的JavaScript内容
    """
    # 移除注释
    content_no_comments = re.sub(r'//.*', '', original_content)

    # 替换键值对
    def replace_value(match):
        key_quote = match.group(1)
        key = match.group(2)
        value_quote = match.group(3)

        if key in translated_dict:
            new_value = escape_js_string(translated_dict[key], value_quote)
            return f'{key_quote}{key}{key_quote}: {value_quote}{new_value}{value_quote}'
        else:
            return match.group(0)  # 保持原样

    pattern = r'''
        (['\"`])([^'\"`]+?)\1\s*:\s*  # 匹配键
        (['\"`])((?:[^\\]|\\.)*)?\3   # 匹配值
    '''

    result = re.sub(pattern, replace_value, content_no_comments, flags=re.VERBOSE | re.DOTALL)

    return result


def translate_js_file_content(content: str, llm_path: str, company_id: str, delay: float) -> tuple[Optional[str], bool]:
    """
    专门翻译 'export default { "key": "value" }' 格式的JS文件内容
    支持反引号、HTML标签和各种引号格式

    Args:
        content: JS文件内容
        llm_path: AI模型服务路径
        company_id: 公司ID
        delay: 请求间隔时间

    Returns:
        (翻译后的文件内容或None, 是否完全成功)
    """
    # 使用正则表达式提取JS对象部分
    match = re.search(r'export\s+default\s*(\{[\s\S]*\})', content)
    if not match:
        logger.warning("在JS文件中未找到 'export default { ... }' 结构，将尝试作为纯文本翻译。")
        translated = translate_text_with_retry(content, llm_path, company_id)
        return translated, translated is not None

    dict_str = match.group(1)

    # 使用改进的解析方法
    data_dict = parse_js_object_advanced(dict_str)
    if not data_dict:
        logger.error("解析JS文件中的对象失败。将尝试作为纯文本翻译。")
        translated = translate_text_with_retry(content, llm_path, company_id)
        return translated, translated is not None

    translated_dict = {}
    logger.info(f"找到 {len(data_dict)} 个待翻译的键值对。")

    count = 0
    all_success = True

    for key, value in data_dict.items():
        count += 1
        if count == 10:
            break
        if isinstance(value, str) and value.strip():
            logger.info(
                f"  ({count}/{len(data_dict)}) 翻译值 for key '{key}' (key保持不变): '{value[:50]}{'...' if len(value) > 50 else ''}'")

            # 使用带重试机制的翻译函数 - 只翻译value，key保持不变
            translated_value = translate_text_with_retry(value, llm_path, company_id, max_retries=5)
            translated_value = translated_value.replace('\n','')
            logger.debug(translated_value)
            if translated_value:
                translated_dict[key] = translated_value.strip()
                logger.info(f"    -> 翻译成功 for key '{key}' (key未翻译)")
            else:
                logger.error(f"    -> 翻译失败（已重试5次） for key '{key}'，标记文件为失败")
                all_success = False
                break

            time.sleep(delay)
        else:
            # key保持不变，value如果为空也保持不变
            translated_dict[key] = value

    # 只有当所有翻译都成功时才返回完整内容
    if not all_success:
        return None, False
    logger.debug(translated_dict)
    # input()

    # 使用改进的重建方法，保持原有格式
    try:
        output_content = rebuild_js_content(content, translated_dict)
        logger.debug(output_content)
        # input()
        return output_content, True
    except Exception as e:
        logger.error(f"重建JS文件内容时出错: {e}")
        return None, False


def translate_file(source_file: Path, target_file: Path, error_folder: Path, llm_path: str, company_id: str,
                   delay: float):
    """
    翻译单个文件.
    根据文件类型（.js 或其他）选择不同的翻译策略。

    Args:
        source_file: 源文件路径
        target_file: 目标文件路径
        error_folder: 错误文件存放文件夹
        llm_path: AI模型服务路径
        company_id: 公司ID
        delay: API请求间的延迟
    """
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()

        if not content.strip():
            logger.warning(f"文件 {source_file} 为空，跳过翻译。")
            # 确保目标目录存在
            target_file.parent.mkdir(parents=True, exist_ok=True)
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(content)
            return

        logger.info(f"开始处理文件：{source_file}")

        success = False
        translated_content = None

        # 检查是否是JS文件
        if source_file.suffix.lower() == '.js':
            logger.info("检测到JS文件，使用JS专用翻译逻辑。")
            translated_content, success = translate_js_file_content(content, llm_path, company_id, delay)
        else:
            logger.info("使用通用文本翻译逻辑。")
            translated_content = translate_text_with_retry(content, llm_path, company_id, max_retries=5)
            success = translated_content is not None
            time.sleep(delay)

        if success and translated_content:
            # 翻译成功，保存到目标文件夹
            target_file.parent.mkdir(parents=True, exist_ok=True)
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(translated_content)
            logger.info(f"文件翻译成功：{source_file} -> {target_file}")
        else:
            # 翻译失败，复制到错误文件夹
            # 计算相对路径，保持目录结构
            try:
                relative_path = source_file.relative_to(Path(source_file).parts[0])
            except:
                relative_path = source_file.name
            error_file = error_folder / relative_path
            error_file.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy(source_file, error_file)
            logger.error(f"文件翻译失败，已复制到错误文件夹：{source_file} -> {error_file}")

    except Exception as e:
        logger.error(f"处理文件 {source_file} 时出错：{e}。将复制到错误文件夹。")
        # 复制到错误文件夹
        try:
            try:
                relative_path = source_file.relative_to(Path(source_file).parts[0])
            except:
                relative_path = source_file.name
            error_file = error_folder / relative_path
            error_file.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy(source_file, error_file)
            logger.info(f"文件已复制到错误文件夹：{error_file}")
        except Exception as copy_error:
            logger.error(f"复制到错误文件夹时也出错：{copy_error}")


def translate_folder(source_folder: str, target_folder: str, llm_path: str, company_id: str = "translation_company",
                     delay: float = 1.0):
    """
    翻译整个文件夹

    Args:
        source_folder: 源文件夹路径 (en-US)
        target_folder: 目标文件夹路径 (id-ID)
        llm_path: AI模型服务路径
        company_id: 公司ID
        delay: 请求间隔时间（秒）
    """
    source_path = Path(source_folder)
    target_path = Path(target_folder)
    error_path = Path("error")  # 错误文件夹

    if not source_path.exists() or not source_path.is_dir():
        logger.error(f"源文件夹不存在或不是一个文件夹：{source_folder}")
        return

    # 创建错误文件夹
    error_path.mkdir(exist_ok=True)

    supported_extensions = get_supported_file_extensions()

    files_to_process = list(source_path.rglob('*'))
    total_files = len(files_to_process)
    processed_files = 0

    logger.info(f"共找到 {total_files} 个文件和目录。")

    for source_item in files_to_process:
        processed_files += 1
        relative_path = source_item.relative_to(source_path)
        target_item = target_path / relative_path

        logger.info(f"[{processed_files}/{total_files}] 正在处理: {source_item}")

        if source_item.is_dir():
            target_item.mkdir(parents=True, exist_ok=True)
            continue

        if target_item.exists():
            logger.info(f"目标文件已存在，跳过：{target_item}")
            continue

        if source_item.suffix.lower() not in supported_extensions:
            logger.info(f"跳过不支持的文件类型：{source_item}。将直接复制。")
            target_item.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy(source_item, target_item)
            continue

        # 翻译支持的文件
        translate_file(source_item, target_item, error_path, llm_path, company_id, delay)

    logger.info("-" * 50)
    logger.info("所有任务处理完成！")
    logger.info(f"成功翻译的文件存放在：{target_folder}")
    logger.info(f"翻译失败的文件存放在：error")


if __name__ == "__main__":
    # 配置参数
    SOURCE_FOLDER = "en-US"  # 源文件夹路径
    TARGET_FOLDER = "id-ID"  # 目标文件夹路径
    LLM_PATH = "http://10.200.3.163:30052"  # 替换为实际的AI服务地址
    COMPANY_ID = "translation_company"  # 公司ID
    REQUEST_DELAY = 2.0  # 每个API请求的间隔时间（秒）

    print("开始批量翻译...")
    print(f"源文件夹：{SOURCE_FOLDER}")
    print(f"目标文件夹：{TARGET_FOLDER}")
    print(f"AI服务地址：{LLM_PATH}")
    print(f"请求间隔：{REQUEST_DELAY}秒")
    print("-" * 50)

    # 开始翻译
    translate_folder(
        source_folder=SOURCE_FOLDER,
        target_folder=TARGET_FOLDER,
        llm_path=LLM_PATH,
        company_id=COMPANY_ID,
        delay=REQUEST_DELAY
    )

    print("翻译任务完成！")