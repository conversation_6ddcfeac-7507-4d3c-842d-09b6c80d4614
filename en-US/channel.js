export default {
  'add.channel': 'Add channel',
  'disable.channel': 'Disabled',
  'disable.channel.confirm':
    'Do you confirm that you want to disable this channel?',
  'enable.channel': 'Active',
  'enable.channel.confirm':
    'Do you confirm that you want to enable this channel?',
  'update.channel': 'Update',
  'delete.channel': 'Delete',
  'delete.channel.confirm':
    'Do you confirm that you want to delete this channel?',
  'delete.channel.confirm.rule':
    'Do you confirm that you want to delete this rule?',
  'add.rule.channel': 'Add rule',
  'channel.allocation': 'Channels configuration',
  'channel.name': 'Channel name',
  'channel.type': 'Channel',
  'channel.status': 'Status',
  'connect.instance': 'Contact line',
  'channel.operation': 'Action',
  'channel.please.input': 'Please enter',
  'channel.display.name': 'Display name',
  'channel.unique.code': 'Unique code',
  'channel.analytic.rule': 'Resolution rules',
  'channel.test.regularity': 'Test regular expression',
  'channel.input.test.regularity': 'Input test text',
  'channel.running.result': 'Running result',
  'channel.running': 'Running',
  'channel.basic.info': 'Basic information',
  'channel.mail.type': 'Email type',
  'channel.other.email': 'Other email (including Gmail)',
  'channel.instructions.use': 'Usage instructions',
  'channel.dont.config': "Don't know how to configure SES/Gmail? click ",
  'channel.view.help.documents': 'to view the help documents',
  'channel.here': 'here',
  'channel.config.mail': 'Configure email',
  'channel.config.mail.pars': 'Ticket extended field configuration',
  'channel.config.mail.pars1': 'Ticket extended attribute configuration',
  'channel.config.tab.1': 'Ticket SLA configuration',
  'channel.config.tab.5': 'Working hours configuration',
  'channel.config.tab.5.button': 'Working hours configuration',
  'channel.config.tab.5.base': 'Basic information about working hours',
  'channel.config.tab.5.holiday': 'Holidays and festivals',
  'channel.config.tab.5.base.name': 'Name',
  'channel.config.tab.5.base.name.p': 'Please enter the name',
  'channel.config.tab.5.base.des': 'Description',
  'channel.config.tab.5.base.des.p': 'Please enter a description',
  'channel.config.tab.5.base.zone': 'Select time zone',
  'channel.config.tab.5.time.add': 'Add time',
  'channel.config.tab.5.base.timezone': 'Time zone',
  'channel.config.tab.5.base.time.work': 'Choosing working hours',
  'channel.config.tab.5.base.date': 'Select date range',
  'channel.config.tab.5.date.add': 'Add date',
  'channel.config.tab.5.week.error': 'Work hours cannot overlap!',
  'channel.config.tab.5.week.error.kong': 'Working hours cannot be empty!',
  'channel.config.tab.2': 'Ticket attributes',
  'channel.config.tab.3': 'Ticket types',
  'channel.config.tab.4': 'Merge ticket configuration',
  'channel.config.tab.1.col.1': 'Priority',
  'channel.config.tab.1.col.2': 'First response time',
  'channel.config.tab.1.col.3': 'Resolution time',
  'channel.config.tab.1.col.4': 'Next avg response time',
  'channel.config.tab.3.add': 'Edit ticket types',
  'channel.config.tab.3.label': 'Display name of ticket type',
  'channel.config.tab.3.label2': 'Value of ticket type',
  'channel.config.connect.pars': 'Integration With Workspace',
  'channel.add.rule': 'Add rules',
  'channel.rule.give': 'Example: Fill in the matching regular expression here',
  'channel.match.positive.num': 'Match all positive numbers',
  'channel.match.decimals.num': 'Match all decimal numbers',
  'channel.not.yet.realized':
    'Not implemented yet, please contact Goclouds Data',
  'channel.contact.number': 'Phone',
  'channel.resume': 'Continue',
  'channel.cancel': 'Cancel',
  'channel.save': 'Save',
  'channel.connect.flow': 'Workflow',
  'channel.connect.template': 'Template',
  'channel.check.channel.name': 'Channel name cannot exceed 20 characters',
  'channel.check.channel.email.type': 'Please select the email type',
  'channel.check.channel.name.not.empty': 'Channel name cannot be empty',
  'channel.check.config.mail.not.empty':
    'The information in the [Configuration Email] is required.',
  'channel.check.connect': 'Enable Workspace Integration',
  'channel.time.day': 'Day',
  'channel.time.minute': 'Minutes',
  'channel.time.hour': 'Hour',
  'priority-p1': 'P1-Service Crash',
  'priority-p2': 'P2-Emergency Handling',
  'priority-p3': 'P3-Severe Impact',
  'priority-p4': 'P4-Intermediate Impact',
  'priority-p5': 'P5-Low level impact',
  'channel.tipe1':
    'This is important information to know when performing an operation.',
  // 'channel.tipe2':'您确定要为"{connectName}",保存渠道"{channelName}"么？',
  'channel.tipe2':
    'Are you sure you want to save channel "{channelName}" for "{connectName}"',
  'merge.work.order.configuration.channel.type': 'Channel Type',
  'merge.work.order.configuration.open.ticket': 'Merge Tickets',
  // 新版渠道配置
  'channel.configuration.online.chat.title': 'Live chat',
  'channel.configuration.voice.video.title': 'Audio and Video',
  'channel.configuration.social.media.title': 'Social media',
  'channel.configuration.email.title': 'Email',
  'channel.configuration.online.retailers.title': 'E-commerce',
  'channel.configuration.other.title': 'Others',
  'channel.configuration.start.configuring': 'Start configuration',
  'channel.configuration.start.configuring.display.tips':
    'Unsupported channel, click Here for Supported Sites',
  'channel.configuration.coming.soon.tips': 'Coming soon, stay tuned!',
  // 渠道配置详情列表
  'channel.allocation.detail.web.online.chat.title': 'WEB Chat Configuration',
  'channel.allocation.detail.app.online.chat.title':
    'APP Chat Channel Configuration',
  'channel.allocation.detail.web.online.voice.title':
    'Web Live Voice Configuration',
  'channel.allocation.detail.app.online.voice.title':
    'APP Live Voice Configuration',
  'channel.allocation.detail.web.online.video.title':
    'Web Live Video Configuration',
  'channel.allocation.detail.app.online.video.title':
    'App Live Video Configuration',
  'channel.allocation.detail.whats.app.title': 'WhatsApp Channel Configuration',
  'channel.allocation.detail.phone.title': 'Phone Channel Configuration',
  'channel.allocation.detail.facebook.title':
    'Facebook Messenger Channel Configuration',
  'channel.allocation.detail.instagram.title':
    'Instagram Channel Configuration',
  'channel.allocation.detail.line.title': 'Line Channel Configuration',
  'channel.allocation.detail.twitter.title': 'Twitter Channel Configuration',
  'channel.allocation.detail.telegram.title': 'Telegram Channel Configuration',
  'channel.allocation.detail.we.com.title':
    'WeChat Customer Service Channel Configuration',
  'channel.allocation.detail.wechat.mini.program.title':
    'WeChat Mini Program Channel Configuration',
  'channel.allocation.detail.wechat.official.account.title':
    'WeChat Official Account Channel Configuration',
  'channel.allocation.detail.email.title': 'Email Channel Configuration',
  'channel.allocation.detail.amazon.message.title':
    'Amazon Message Channel Configuration',
  'channel.allocation.detail.shopify.title': 'Shopify Channel Configuration',
  'channel.allocation.detail.google.play.title':
    'Google Play Channel Configuration',
  'channel.allocation.detail.select.channel.name': 'Channel name: ',
  'channel.allocation.detail.select.channel.name.placeholder':
    'Please enter the channel name and press Enter to search.',
  'channel.allocation.detail.select.language': 'Language: ',
  'channel.allocation.detail.select.language.placeholder':
    'Please select language',
  'channel.allocation.detail.select.website.domain.name': 'Website domain: ',
  'channel.allocation.detail.select.website.domain.name.placeholder':
    'Please enter the website domain name and press Enter to search.',
  'channel.allocation.detail.select.phone.number': 'Phone number: ',
  'channel.allocation.detail.select.phone.number.placeholder':
    'Please enter a phone number and press Enter to search.',
  'channel.allocation.detail.select.email.account': 'Email accounts: ',
  'channel.allocation.detail.select.email.account.placeholder':
    'Please enter your email account, then press Enter to search.',
  'channel.allocation.detail.select.bind.phone.number':
    'WhatsApp phone number: ',
  'channel.allocation.detail.select.bind.phone.number.placeholder':
    'Please enter the phone number, then press Enter to search.',
  'channel.allocation.detail.select.account.name': 'Account name: ',
  'channel.allocation.detail.select.account.name.placeholder':
    'Please enter the account name and press Enter to search.',
  'channel.allocation.detail.select.account.id': 'Account ID: ',
  'channel.allocation.detail.select.account.id.placeholder':
    'Please enter your account ID and press Enter to search.',
  'channel.allocation.detail.select.line.id': 'Line ID: ',
  'channel.allocation.detail.select.line.id.placeholder':
    'Please enter Line ID, press enter to search.',
  'channel.allocation.detail.select.enterprise.name': 'Company full name: ',
  'channel.allocation.detail.select.enterprise.name.placeholder':
    'Please enter the full company name, press Enter to search.',
  'channel.allocation.detail.select.enterprise.id': 'Customer service name: ',
  'channel.allocation.detail.select.enterprise.id.placeholder':
    'Please enter the customer service name and press enter to search.',
  'channel.allocation.detail.access.link': 'Connection link',
  'channel.allocation.detail.access.QRCode': 'The QR code generated',
  'channel.allocation.detail.select.app.id': 'APP ID: ',
  'channel.allocation.detail.select.app.id.placeholder':
    'Please enter the APP ID and press Enter to search.',
  'channel.allocation.detail.select.seller.region': "Seller's Region: ",
  'channel.allocation.detail.select.seller.region.placeholder':
    "Please select the seller's region",
  'channel.allocation.detail.add.channel.text': 'Add channel',
  'statistics.data.details.channel.operation.arrange': 'Deployment',
  'statistics.data.details.channel.configuration.information': 'Configuration',
  'statistics.data.details.channel.view.applications': 'View all applications',
  // 添加Line渠道配置
  'add.line.channel.configuration.title': 'Add Line Channel',
  'editor.line.channel.configuration.title': 'Modify Line channel',
  'add.line.channel.configuration.tips':
    'Simplify customer interactions via Line, enabling faster and more effective responses to their inquiries.',
  'line.channel.configuration.title.1': 'Fill in basic channel information',
  'line.channel.configuration.title.tips.1':
    'Please fill in basic information.',
  'line.channel.configuration.title.2': 'Fill in Line info',
  'line.channel.configuration.title.tips.2':
    'Please fill in the information related to Line.',
  'line.channel.configuration.title.tips.2.1': 'Click',
  'line.channel.configuration.title.tips.2.2': ' "Help Documentation" ',
  'line.channel.configuration.title.tips.2.3':
    'See how to get the following information',
  'line.channel.configuration.channel.app.name': 'App name',
  'line.channel.configuration.channel.app.name.placeholder':
    'Enter the app name',
  'line.channel.configuration.channel.id': 'Channel ID',
  'line.channel.configuration.channel.id.placeholder':
    'Please enter the channel ID.',
  'line.channel.configuration.channel.secret': 'Channel Secret',
  'line.channel.configuration.channel.secret.placeholder':
    'Please enter the Channel secret.',
  'line.channel.configuration.channel.line.id': 'Line ID',
  'line.channel.configuration.channel.line.id.placeholder':
    'Please enter your Line ID.',
  'line.channel.configuration.channel.access.token':
    'Channel Access Token (Long Lived)',
  'line.channel.configuration.channel.access.token.placeholder':
    'Please enter Channel Access Token (Long Lived).',
  'line.channel.configuration.channel.app.name.1': 'APP name: ',
  'line.channel.configuration.channel.id.1': 'Channel ID: ',
  'line.channel.configuration.channel.secret.1': 'Channel Secret: ',
  'line.channel.configuration.channel.line.id.1': 'Line ID: ',
  'line.channel.configuration.channel.access.token.1':
    'Channel Access Token (Long Lived): ',
  'line.channel.configuration.title.3': 'Intelligent customer service settings',
  'line.channel.configuration.title.tips.3':
    'Here you can configure information related to the intelligent customer service.',
  'line.channel.configuration.customer.service.mode': 'Customer service mode',
  'line.channel.configuration.customer.service.mode.tips':
    'AI customer service will first respond with a chatbot. For questions that the chatbot cannot answer, users can transfer to a human agent at any time.',
  'line.channel.configuration.emotional.version': 'Chatbot mode',
  'line.channel.configuration.emotional.version.concise.clear':
    'Professional Mode',
  'line.channel.configuration.emotional.version.gentle.companionship':
    'Emotionally Intelligent Mode (Softer Responses)',
  'line.channel.configuration.emotional.version.tips':
    'By enabling "Emotionally Intelligent Mode", the AlGC Chatbot will deliver softer responses to your customers.',
  'line.channel.configuration.title.4': 'Line Webhook Configuration',
  'line.channel.configuration.title.tips.4': 'Configuring Webhook on Line',
  'line.channel.configuration.title.tips.4.1':
    'Please copy the following link, paste it into the Webhook URL of your Line official account, and click "Verify". For configuration instructions, click ',
  'line.channel.configuration.title.tips.4.2': '"Help Documentation"',
  'line.channel.configuration.title.tips.4.3': '.',
  // 添加微信公众号渠道
  'add.web.chat.official.account.channel.configuration.title':
    'Add WeChat official account channel',
  'editor.web.chat.official.account.channel.configuration.title':
    'Modify WeChat public account channel',
  'add.web.chat.official.account.channel.configuration.tips':
    'Simplify customer interactions via WeChat Official Accounts, enabling faster and more effective responses to their inquiries.',
  'add.web.chat.official.account.channel.configuration.2':
    'Fill in WeChat Official Account information',
  'add.web.chat.official.account.channel.configuration.tips.2':
    'Please fill in the relevant information for the WeChat official account.',
  'add.web.chat.official.account.channel.configuration.app.id': 'APP ID',
  'add.web.chat.official.account.channel.configuration.app.id.placeholder':
    'Please enter the APP ID.',
  'add.web.chat.official.account.channel.configuration.app.secret':
    'App Secret',
  'add.web.chat.official.account.channel.configuration.app.secret.placeholder':
    'Please enter APP Secret.',
  'add.web.chat.official.account.channel.configuration.app.id.1': 'APP ID: ',
  'add.web.chat.official.account.channel.configuration.app.secret.1':
    'App Secret: ',
  'add.web.chat.official.account.channel.configuration.4':
    'Configure WeChat official account',
  'add.web.chat.official.account.channel.configuration.tips.4':
    'Configure Webhook URL in WeChat official account',
  'add.web.chat.official.account.channel.configuration.tips.4.1':
    'Please copy the following link and paste it into the',
  'add.web.chat.official.account.channel.configuration.tips.4.2':
    ' "WeChat Official Accounts Platform." ',
  'add.web.chat.official.account.channel.configuration.tips.4.3': 'Click ',
  'add.web.chat.official.account.channel.configuration.tips.4.4':
    '"Help Documentation"',
  'add.web.chat.official.account.channel.configuration.tips.4.5':
    ' for instructions on configuring the Webhook URL. ',
  'add.web.chat.official.account.channel.configuration.tips.4.6':
    'After that, visit the official platform to complete the settings.',
  'add.web.chat.official.account.channel.configuration.official.account.id':
    'Public account ID: ',
  'add.web.chat.official.account.channel.configuration.official.account.id.placeholder':
    'Please enter your WeChat official account ID.',
  // Google Play 所有应用
  'channel.allocation.google.play.all.apps': 'All Google Play Apps',
  'channel.allocation.google.play.all.apps.code': 'application number: ',
  'channel.allocation.google.play.all.apps.view.detail': 'Comment details',
  // 评论反馈
  'comment.feedback.title': 'Google Play Reviews',
  'comment.feedback.select.date.seven': 'Last 7 days',
  'comment.feedback.select.date.thirty': 'In the past 30 days',
  'comment.feedback.select.date.ninety': 'Last 90 days',
  'comment.feedback.select.date.past.year': 'In the past year',
  'comment.feedback.select.date.custom': 'Custom',
  'comment.feedback.select.rating': 'Select rating',
  'comment.feedback.enter.keywords': 'Keywords',
  'comment.feedback.enter.keywords.placeholder':
    'Please enter the comment content.',
  'comment.feedback.enter.device.language': 'Device language',
  'comment.feedback.enter.device': 'Device',
  'comment.feedback.enter.device.placeholder': 'Please select a device.',
  'comment.feedback.application.version.code': 'App version code',
  'comment.feedback.enter.form.1': 'From ',
  'comment.feedback.replay.time': 'Reply time: ',
  'comment.feedback.application.version.code.placeholder':
    'Please select the application version code.',
  'comment.feedback.application.version.name': 'App version name',
  'comment.feedback.application.version.name.placeholder':
    'Please select the application version name.',
  'comment.feedback.android.version.sdk': 'Android versions (SDK)',
  'comment.feedback.android.version.sdk.placeholder':
    'Please select Android version (SDK)',
  'comment.feedback.advanced.screening': 'Advanced filter',
  'comment.feedback.all.comments': 'All Comments',
  'comment.feedback.reply.comment': 'Replied Comments',
  'comment.feedback.no.response.comments': 'Unreplied Comments',
  'comment.feedback.updated.after.reply': 'Updated After Reply',
  'comment.feedback.deleted': 'Deleted',
  'comment.feedback.batch.reply.btn': 'Bulk Reply',
  'comment.feedback.comment.time.text': 'Comment time: ',
  'comment.feedback.enter.device.language.1': 'Device language: ',
  'comment.feedback.application.version.code.1': 'Application version code: ',
  'comment.feedback.application.version.name.1': 'Application version name:',
  'comment.feedback.professional.response': 'AIGC Professional Reply',
  'comment.feedback.personal.remarks': 'Private notes',
  'comment.feedback.replay.content.placeholder':
    'Please enter the content you wish to reply with.',
  'comment.feedback.replay.content.placeholder.2':
    'Please enter your content, limited to 1024 characters',
  'comment.feedback.notes.time': 'Remark time: ',
  'comment.feedback.historical.review': 'Historical Comments',
  'comment.feedback.personal.remarks.modal': 'Private notes',
  'comment.feedback.personal.remarks.modal.placeholder':
    'Please enter private notes',
  'comment.feedback.batch.reply.modal': 'Bulk reply',
  'comment.feedback.batch.reply.modal.placeholder': 'Please enter',
  'comment.feedback.translate.text': 'Translation: ',
  'comment.feedback.application.package.name': 'application package name:',
  'comment.feedback.remark.success': 'Note added successfully',
  'comment.feedback.remark.failed':
    'Failed to add note. Please try again later.',
  'comment.feedback.replay.success': 'Reply successful',
  'comment.feedback.replay.failed': 'Retry failed, please try again later.',
  'channel.config.tab.1.add.rule.btn': 'Add  SLA rule',
  'channel.config.tab.1.editor.rule.btn': 'Modify SLA rule',
  'channel.config.tab.1.delete.rule.tips': 'Are you sure you want to delete this rule?',
  'channel.config.tab.1.add.rule.name': 'Rule Name: ',
  'channel.config.tab.1.add.rule.name.placeholder':
    'Please enter the rule name',
  'channel.config.tab.1.add.rule.name.required.max':
    'The rule name can be up to 80 characters',
  'channel.config.tab.1.add.rule.num.required.max':
    'The number cannot exceed 100000000',
};
