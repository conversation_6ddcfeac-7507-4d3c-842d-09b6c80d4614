export default {
  'help.document.title': 'Help documentation',
  'help.document.whats.app.title':
    'How to register a whatsapp business account',
  'help.document.line.title': 'How to register line channel',
  'help.document.we.chat.official.title':
    'How to configure wechat official account channel information',
  // line帮助文档
  'help.document.line.left.menu': 'Create line official account',
  'help.document.line.left.menu.1':
    'Get line channel configuration information',
  'help.document.line.left.menu.2': 'Integrate line into connectnow',
  'help.document.line.step.1.title': 'Step 1:',
  'help.document.line.step.2.title': 'Step 2:',
  'help.document.line.step.3.title': 'Step 3:',
  'help.document.line.step.4.title': 'Step 4:',
  'help.document.line.step.5.title': 'Step 5:',
  'help.document.line.step.6.title': 'Step 6:',
  'help.document.line.step.7.title': 'Step 7:',
  'help.document.line.step.9.title': 'Step 8:',
  'help.document.line.step.10.title': 'Step 9:',
  'help.document.line.step.1.text.1':
    'Open link: <a>https://tw.linebiz.com/account/</a>, click "create free account"',
  'help.document.line.step.1.text.2':
    'Choose to log in with line account or business account',
  'help.document.line.step.1.text.3':
    'If unsure which to use, please refer to the explanation on the line official website:<a>"what is the difference between logging in with a line account and logging in with a business account?"</a>',
  'help.document.line.step.1.text.4':
    'After logging in, fill in the line official account information, review the "line official account terms of service" and then click "confirm"',
  'help.document.line.step.1.text.5':
    'After confirming the entered content is correct, click "submit"',
  'help.document.line.step.1.text.6':
    'If the following screen is displayed, it means the line official account application was successful',
  'help.document.line.step.2.text':
    'If you already have a line official account, click <a>line official account manager</a> to log in',
  'help.document.line.step.2.text.1':
    'After logging into “line official account manager”, first select the line official account to integrate:',
  'help.document.line.step.2.text.2': 'Click "settings"',
  'help.document.line.step.2.text.3':
    'Select "messaging API" on the left, then on this messaging API page, click "enable messaging API"',
  'help.document.line.step.2.text.4':
    'After clicking "enable messaging API", you first need to select an existing service provider, or create a new service provider (provider). after selection, please read the "line official account API terms of service" carefully and then click "agree".',
  'help.document.line.step.2.text.5':
    'Next, fill in the "URL for privacy policy and terms of service (optional)". if not filled in, you can still click "confirm" to proceed to the next step',
  'help.document.line.step.2.text.6':
    'After confirming that the "account name" and "service provider name" are correct, click "confirm" to officially enable messaging API',
  'help.document.line.step.2.text.7':
    'After clicking "confirm", the following screen will appear and the status will be "in use"',
  'help.document.line.step.2.text.8':
    'Next, you can click "line developers" to get more data needed for integration!',
  'help.document.line.step.2.text.9':
    'To integrate line into ConnectNow backend, you will need to copy the following 5 pieces of data:',
  'help.document.line.step.2.text.10': '- App name',
  'help.document.line.step.2.text.11': '- Channel id',
  'help.document.line.step.2.text.12': '- Channel secret',
  'help.document.line.step.2.text.13': '- Channel access token (long lived)',
  'help.document.line.step.2.text.14': '- Line official account id',
  'help.document.line.step.2.text.15':
    'On the messaging API page, click to enter line developers',
  'help.document.line.step.2.text.16':
    'Tip: channel ID and channel secret will be displayed on the messaging API page, and you can also copy these two pieces of information from this page',
  'help.document.line.step.2.text.17':
    'After entering line developers, first click the avatar in the upper right corner, and select line account',
  'help.document.line.step.2.text.18':
    'From the admin section on the left, select provider',
  'help.document.line.step.2.text.19':
    'Then click channel (official account) to enter the settings page',
  'help.document.line.step.2.text.20':
    'After entering the settings page, obtain the data needed for connection from the "basic settings" and "messaging API" tabs respectively',
  'help.document.line.step.2.text.21':
    'The first three items can be obtained in "basic settings"',
  'help.document.line.step.2.text.22': 'Switch to messaging API tab',
  'help.document.line.step.2.text.23':
    'Scroll down the page, click the issue button to get channel access token (long lived)',
  'help.document.line.step.2.text.24':
    'After clicking, you can see the channel access token (long-lived) information',
  'help.document.line.step.2.text.25': 'How to get line official account id',
  'help.document.line.step.2.text.26':
    'Enter line official account backend: <a>https://manager.line.biz</a>',
  'help.document.line.step.2.text.27':
    'Copy the text highlighted in red in the image below to get the line id of the line official account (excluding @)',
  'help.document.line.step.3.text':
    'Log in to connectnow platform with administrator account, click channel configuration->line->add',
  'help.document.line.step.3.text.1': 'Click add channel',
  'help.document.line.step.3.text.2':
    'After entering the channel name, click next, the following page will appear, please paste the 5 pieces of data you just obtained into the corresponding data boxes.',
  'help.document.line.step.3.text.3':
    'After completing the input, click next to proceed with the robot related settings',
  'help.document.line.step.3.text.4': 'Get webhook address',
  'help.document.line.step.3.text.5':
    "Return to line developers' message API tab",
  'help.document.line.step.3.text.6':
    'Find webhook settings, and click "edit" here at webhook url',
  'help.document.line.step.3.text.7':
    'Paste the line webhook url just copied from the connectnow backend, click "update"',
  'help.document.line.step.3.text.8':
    "Press' Verify 'to make sure you see' Success',",
  'help.document.line.step.3.text.9':
    'Note: please ensure to click complete on the connectnow platform before proceeding with "verify", otherwise it will result in failure.',
  'help.document.line.step.3.text.10': 'Open use webhook switch',
  'help.document.line.step.3.text.11':
    'If you cannot enable use webhook in line developers, you can adjust it in the "response settings" of the line OA backend, and check "enable" in the webhook section',
  'help.document.line.step.3.text.12':
    'If you will not reply to messages in the line OA native backend, but only in the ConnectNow backend, please make the following settings in the line OA backend:',
  'help.document.line.step.3.text.13':
    '- Turn off "chat switch"; enable webhook',
  'help.document.line.step.3.text.14':
    'If you need to reply to customer messages in the line OA backend, please make sure to make the following settings in line OA "response settings":',
  'help.document.line.step.3.text.15': '- Turn on "chat switch";',
  'help.document.line.step.3.text.16': '- Enable webhook',
  'help.document.line.step.3.text.17':
    '- Turn off "welcome message for adding friends" (to avoid triggering welcome message functions on both line OA and ConnectNow simultaneously)',
  'help.document.line.step.3.text.18':
    '- Turn off "response time" (to avoid triggering offline instant messaging functions on both line OA and ConnectNow simultaneously)',
  'help.document.line.step.3.text.19':
    '- For "chat response method", please be sure to select "manual" (to avoid simultaneously triggering bot responses, keyword auto-replies, and welcome messages on both line OA and ConnectNow)',
  'help.document.line.step.3.text.20':
    'Completed! your line has been successfully integrated into connectnow',
  // 微信公众号帮助文档
  'help.document.we.chat.official.left.menu':
    'Log in to wechat official platform',
  'help.document.we.chat.official.left.menu.1':
    'Get wechat official account id',
  'help.document.we.chat.official.left.menu.2':
    'Connectnow channel configuration',
  'help.document.we.chat.official.step.1.text':
    'Log in to wechat official platform <a>go to official platform</a>',
  'help.document.we.chat.official.step.1.text.1':
    'Open wechat on your mobile phone, click the "+" in the upper right corner, select "scan", after scanning the QR code, select the organization to log in, and the official platform will automatically redirect',
  'help.document.we.chat.official.step.2.text':
    'To get the official account id, select settings and development->official account settings from the left menu, scroll down to find the original id',
  'help.document.we.chat.official.step.2.text.1':
    'Get APP ID, app secret, select settings and development->basic configuration from the left menu',
  'help.document.we.chat.official.step.3.text':
    'Get parameters through connectnow channel configuration',
  'help.document.we.chat.official.step.3.text.1':
    'Fill the obtained configuration into the official platform, click modify configuration',
  'help.document.we.chat.official.step.3.text.2':
    'Configure the parameters obtained in step 4 of connectnow channel configuration into the corresponding parameters',
  'help.document.we.chat.official.step.3.text.3': '',
  // WhatsApp帮助文档
  'help.document.whats.app.left.menu':
    'Materials to prepare before getting started',
  'help.document.whats.app.left.menu.1': 'Create new account',
  'help.document.whats.app.left.menu.18': 'Click create entry',
  'help.document.whats.app.left.menu.2': 'Log in to facebook',
  'help.document.whats.app.left.menu.3': 'Confirm agreement',
  'help.document.whats.app.left.menu.4': 'Create BM account',
  'help.document.whats.app.left.menu.5': 'Create WABA',
  'help.document.whats.app.left.menu.6': 'Set WABA and number information',
  'help.document.whats.app.left.menu.7': 'Bind mobile number',
  'help.document.whats.app.left.menu.8': 'Verification code validation',
  'help.document.whats.app.left.menu.9': 'Second confirmation',
  'help.document.whats.app.left.menu.10': 'Creation successful',
  'help.document.whats.app.left.menu.11': 'Binding successful',
  'help.document.whats.app.left.menu.12': 'Complete channel addition',
  'help.document.whats.app.left.menu.13': 'Business verification',
  'help.document.whats.app.left.menu.14': 'Business verification preparation',
  'help.document.whats.app.left.menu.15': 'Upload supporting documents',
  'help.document.whats.app.left.menu.16': 'Select contact method',
  'help.document.whats.app.left.menu.17': 'Wait for verification result',
  'help.document.whats.app.step.1.table.title': 'Prepare materials',
  'help.document.whats.app.step.1.table.title.1': 'Example',
  'help.document.whats.app.step.1.table.title.2': 'Specific requirements',
  'help.document.whats.app.step.1.table.body': 'Facebook personal account',
  'help.document.whats.app.step.1.table.body.1': '-',
  'help.document.whats.app.step.1.table.body.2':
    'An old account registered for at least one month (before starting, you can click the link to check if the account is normal), used to create meta business manager (BM for short)',
  'help.document.whats.app.step.1.table.body.3': 'Mobile number',
  'help.document.whats.app.step.1.table.body.4': '+1 ***********',
  'help.document.whats.app.step.1.table.body.5':
    'Needs to be able to receive verification code SMS; this number must not have been previously registered for a whatsapp app or business account (if it was previously registered for a personal whatsapp account, the personal whatsapp account must be cancelled before use); used to create a whatsapp business account.',
  'help.document.whats.app.step.1.table.body.5.1':
    'Note: chinese mainland mobile numbers are also acceptable',
  'help.document.whats.app.step.1.table.body.6':
    'Whatsapp business display name',
  'help.document.whats.app.step.1.table.body.7': 'Connectnow',
  'help.document.whats.app.step.1.table.body.8':
    "Needs to be related to the brand's official website and is used to name the whatsapp business account",
  'help.document.whats.app.step.1.table.body.9': 'Company name',
  'help.document.whats.app.step.1.table.body.10': 'Connectnow',
  'help.document.whats.app.step.1.table.body.11':
    'Must be exactly the same as the company name on the business license or registration certificate',
  'help.document.whats.app.step.1.table.body.12': 'Company address',
  'help.document.whats.app.step.1.table.body.13':
    '7500a beach road #04-307 the plaza singapore 19959',
  'help.document.whats.app.step.1.table.body.14':
    'Must be exactly the same as the address on the business license or registration certificate',
  'help.document.whats.app.step.1.table.body.15':
    'Business license or registration certificate',
  'help.document.whats.app.step.1.table.body.16': 'Business_pofile.pdf',
  'help.document.whats.app.step.1.table.body.17':
    'Business license or registration document corresponding to the enterprise',
  'help.document.whats.app.step.1.table.body.18': 'Brand website',
  'help.document.whats.app.step.1.table.body.19': 'Www.connectnowai.com',
  'help.document.whats.app.step.1.table.body.20':
    '1.URL needs to be HTTPS encrypted;',
  'help.document.whats.app.step.1.table.body.20.1':
    '2.URL content needs to clearly express company business;',
  'help.document.whats.app.step.1.table.body.20.2':
    '3.the bottom of the URL needs to include the company name and address, e.g.: copyright @ xxxx (current year) +company name all rights reserved',
  'help.document.whats.app.step.1.table.body.21': 'Company email address',
  'help.document.whats.app.step.1.table.body.22': '<EMAIL>',
  'help.document.whats.app.step.1.table.body.23':
    'The corporate email suffix needs to be consistent with the brand website domain. for example: www.connectnowai.<NAME_EMAIL> meet the requirements and will be used to receive a one-time verification code email during business verification',
  'help.document.whats.app.step.1.text':
    'Note: if you already have a BM account (facebook business manager) and wish to create a whatsapp API account under the existing BM account, you can log in to the facebook account of that BM account administrator in step 2 of the following process, and select the existing BM account in step 4.',
  'help.document.whats.app.step.2.text':
    'After preparing the above materials, you can log in to connectnow as an administrator, click channel configuration->whatsapp->add->bind whatsapp in the left menu to start the embedded registration process',
  'help.document.whats.app.step.2.text.1':
    'Click "channel configuration", scroll down to find whatsapp, click "start configuration"',
  'help.document.whats.app.step.2.text.2':
    'Enter the "whatsapp channel configuration" interface, click "add channel"',
  'help.document.whats.app.step.2.text.3':
    'Enter the "add whatsapp channel" page, click bind whatsapp account, enter facebook login page',
  'help.document.whats.app.step.2.text.4':
    'After clicking bind “whatsapp account”, a facebook authorization pop-up will appear. please log in with your prepared facebook account. if the account is incorrect, you can click "log in another account" to switch. after confirming the account is correct, click continue to proceed to the next step.',
  'help.document.whats.app.step.2.text.5':
    'After confirming correctness, click start to continue creation.',
  'help.document.whats.app.step.2.text.6': 'Enter your company information:',
  'help.document.whats.app.step.2.text.7':
    '*Company name: the company name must be exactly the same as the name on the business license. do not use abbreviations or brand names.',
  'help.document.whats.app.step.2.text.8':
    '*Company email: it is recommended to use an email address with the same website domain.',
  'help.document.whats.app.step.2.text.9':
    "*Company website or business homepage: company URL. note that the company's URL must be HTTPS protocol.",
  'help.document.whats.app.step.2.text.10':
    '*Country/region: the country of operation for the company. please be sure to select the country of affiliation for the company you intend to certify. for example, if you submit an indonesian business license for business verification, then select indonesia as the country.',
  'help.document.whats.app.step.2.text.11':
    'Note: if you already have a BM account, you can select it in the business portfolio option.',
  'help.document.whats.app.step.2.text.12':
    'Select/create a new WABA. if an existing WABA is available, you can select it from the dropdown, otherwise choose create.',
  'help.document.whats.app.step.2.text.13':
    '*Whatsapp business account name: the name of the WABA. used for internal business differentiation, your audience will not see this information on your whatsapp account profile.',
  'help.document.whats.app.step.2.text.14':
    '*Whatsapp display name: the name of the number. the name ultimately seen by customers, needs to be related to the company name, or to the brand name. click to view display name guidelines.',
  'help.document.whats.app.step.2.text.15': '*Category: industry',
  'help.document.whats.app.step.2.text.16':
    'Enter the mobile number for registering the whatsapp API account, select the method to get the verification code:',
  'help.document.whats.app.step.2.text.17': '1. SMS',
  'help.document.whats.app.step.2.text.18': '2. voice call',
  'help.document.whats.app.step.2.text.19':
    'After completing the input, click next.',
  'help.document.whats.app.step.2.text.20':
    'Tip: for chinese mainland region (+86), it is recommended to use voice call to receive the verification code.',
  'help.document.whats.app.step.2.text.21':
    'Enter the received verification code and click next',
  'help.document.whats.app.step.2.text.22':
    'Confirm your creation content, click continue if correct',
  'help.document.whats.app.step.2.text.23':
    'Creation successful message will be prompted, please be sure to click the complete button below the pop-up.',
  'help.document.whats.app.step.2.text.24':
    'When the pop-up closes, connectnow will proceed with binding.',
  'help.document.whats.app.step.2.text.25': 'Select number',
  'help.document.whats.app.step.2.text.26': 'Set up robot',
  'help.document.whats.app.step.2.text.27':
    'After entering the channel name and clicking complete, the registration of the whatsapp business account is complete',
  'help.document.whats.app.step.3.text':
    'Prepare and verify your BM account to send unlimited messages on whatsapp daily.',
  'help.document.whats.app.step.3.text.1':
    'BM business verification is a process aimed at verifying whether the BM account belongs to a real organization.',
  'help.document.whats.app.step.3.text.2':
    'If you have not completed BM business verification, you will be subject to restrictions when using the whatsapp business API, including:',
  'help.document.whats.app.step.3.text.3':
    'Sending business-initiated conversations to 250 unique customers on a rolling 24-hour basis per phone number.',
  'help.document.whats.app.step.3.text.4':
    'Register a maximum of 2 phone numbers.',
  'help.document.whats.app.step.3.text.5':
    'After completing business verification and display name review, your business will have the opportunity to quickly lift restrictions:',
  'help.document.whats.app.step.3.text.6':
    'Extend business-initiated conversations to more customers: starting from 1,000 unique customers in a rolling 24-hour period, gradually increasing to 10,000, 100,000, or unlimited per phone number.',
  'help.document.whats.app.step.3.text.7':
    'Respond to unlimited customer-initiated conversations.',
  'help.document.whats.app.step.3.text.8':
    'Request to become an official business account (OBA).',
  'help.document.whats.app.step.3.text.9':
    'Register additional phone numbers (maximum 20 per BM).',
  'help.document.whats.app.step.3.text.10':
    'As of April 2024, after meeting message quality standards and completing display name review, businesses can display their name in chats, increasing customer trust, without requiring business verification. please refer to meta documentation:',
  'help.document.whats.app.step.3.text.11':
    'Https://developers.facebook.com/docs/whatsapp/messaging-limits#open-1k-conversations-in-30-days',
  'help.document.whats.app.step.3.text.12':
    "To increase your company's chances of passing meta verification, you need to confirm the following information in advance:",
  'help.document.whats.app.step.3.text.13':
    "The company's official website address: and HTTPS encrypted, and containing the company's name, address, or phone number",
  'help.document.whats.app.step.3.text.14':
    'Email address with the same domain as the company website: it will be used to receive a one-time verification email (not required for domain verification or mobile number verification, but generally, we recommend email verification)',
  'help.document.whats.app.step.3.text.15':
    "Official documents containing the company's legal name: such as business license, articles of association, or business tax registration certificate",
  'help.document.whats.app.step.3.text.16':
    'You need to ensure that the company name included in the document is relevant to the company\'s official website, for example, by entering "belongs to company ABC" in the footer',
  'help.document.whats.app.step.3.text.17': 'Verification process',
  'help.document.whats.app.step.3.text.17.1': '1. verification process',
  'help.document.whats.app.step.3.text.18':
    'If you are an administrator of a meta business manager account (after completing chapter 2, you will automatically become an administrator of the meta business manager account), you can follow these steps to start verifying your business:',
  'help.document.whats.app.step.3.text.19':
    'Go to the <a>"security center"</a> section of the business manager platform.',
  'help.document.whats.app.step.3.text.20':
    'If you do not see the "verify" button, please visit the connectnow platform and complete the embedded registration process (i.e. chapter 2).',
  'help.document.whats.app.step.3.text.21':
    '2. submit organization basic information',
  'help.document.whats.app.step.3.text.22':
    'Provide your organization name, address, phone number, and website',
  'help.document.whats.app.step.3.text.23': 'Best practices',
  'help.document.whats.app.step.3.text.24':
    'The company name/address you enter must be consistent with the name/address on the supporting documents.',
  'help.document.whats.app.step.3.text.25':
    'The phone number can be a personal mobile number (but the company cannot be verified using a mobile number in subsequent steps)',
  'help.document.whats.app.step.3.text.26':
    'The submitted website must contain text proving domain ownership, for example, by entering "belongs to company ABC" in the footer, and this company name must be consistent with the company name you entered.',
  'help.document.whats.app.step.3.text.27':
    'After the upload is complete, install page information and submit for verification',
  'help.document.whats.app.step.3.text.28': 'Best practices',
  'help.document.whats.app.step.3.text.29':
    'Email verification is preferred, but your email address suffix must match the submitted domain (www.mypage.com >> <EMAIL>).',
  'help.document.whats.app.step.3.text.30':
    'Domain verification is the next option to verify your business.',
  'help.document.whats.app.step.3.text.31':
    'After entering the verification code, click next until the final step to submit verification',
  'help.document.whats.app.step.3.text.32':
    'After submitting verification, a decision can be made in as fast as 10 minutes, or up to 14 business days. you will be notified after the review is complete. if you receive confirmation that you have been verified, no further action is required.',
};
