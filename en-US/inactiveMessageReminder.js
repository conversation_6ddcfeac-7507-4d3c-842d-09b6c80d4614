export default {
  'inactive.message.reminder.title': 'Inactivity reminder',
  'inactive.message.reminder.btn.add.reply': 'Add reply',
  'inactive.message.reminder.tab.title.1': 'Customer no-reply reminder',
  'inactive.message.reminder.tab.title.1.tips':
    "Automatically send a reminder to the customer if they don't reply for a set time, during bot or agent conversations (web and app channels only).",
  'inactive.message.reminder.tab.title.2': 'Agent no-reply reminder',
  'inactive.message.reminder.tab.title.2.tips':
    "Automatically send a reminder to the customer if the agent doesn't reply for a set time during a conversation (web and app channels only).",
  'inactive.message.reminder.table.reply.content': 'Reply content',
  'inactive.message.reminder.table.wait.time': 'Wait time',
  'inactive.message.reminder.modal.wait.time': 'Wait time:',
  'inactive.message.reminder.modal.wait.time.required': 'Enter wait time',
  'inactive.message.reminder.modal.unit.day': 'Day',
  'inactive.message.reminder.modal.unit.hour': 'Hour',
  'inactive.message.reminder.modal.unit.minute': 'Minute',
  'inactive.message.reminder.modal.unit.placeholder': 'Select time unit',
  'inactive.message.reminder.modal.unit.title': 'Time unit:',
  'inactive.message.reminder.modal.reply.content': 'Reply content:',
  'inactive.message.reminder.modal.reply.content.required':
    'Enter reply content',
  'inactive.message.reminder.modal.wait.time.max':
    'Wait time must be between 1 and 100,000,000',
};
