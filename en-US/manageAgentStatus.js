export default {
  'manage.agent.status.title': 'Manage Agent Status',
  'manage.agent.status.add.btn': 'Add agent status',
  'manage.agent.status.table.status.name': 'Status name',
  'manage.agent.status.table.describe': 'Description',
  'manage.agent.status.table.type': 'Type',
  'manage.agent.status.table.enabled': 'Enabled',
  'manage.agent.status.table.operation': 'Operation',
  'manage.agent.status.confirm.delete.tips':
    'Confirm to delete this agent status?',
  'manage.agent.status.modal.status.name': 'Status name: ',
  'manage.agent.status.modal.status.name.tips': 'Please enter the status name',
  'manage.agent.status.modal.type': 'Type: ',
  'manage.agent.status.modal.type.tips': 'Please select type',
  'manage.agent.status.type.list.route.able': 'Route able',
  'manage.agent.status.type.list.offline': 'Offline',
  'manage.agent.status.modal.describe': 'Description: ',
  'manage.agent.status.modal.describe.tips': 'Please enter description',
  'manage.agent.status.modal.status.name.required.max':
    'Status name up to 80 characters',
  'manage.agent.status.modal.status.description.required.max':
    'Description up to 2000 characters',
};
