export default {
  'home.page.menu.home': 'Home',
  'home.page.menu.cloud.contact.center': 'Cloud Contact Center',
  'home.page.menu.contact.us': 'Contact US',
  'home.page.menu.free.trial': 'Login',
  'home.page.menu.experience.btn': 'Free Trail!',
  'home.page.menu.tips.text':
    'Customer experience is the key to business growth. Every interaction with a customer is an opportunity that should not be missed. With ConnectNow, channels and time zones will no longer limit your ability to communicate with customers.',
  'home.page.text.aigc': 'AI Assistant-CoCo',
  'home.page.text.aigc.detail':
    'With multilingual online translation capabilities, break the language barriers and provide customers with <b>{US}</b> services;',
  'home.page.text.aigc.span': 'globalized',
  'home.page.text.aigc.detail1':
    'Automatically summarize chat content and extract key information for ticketing;',
  'home.page.text.aigc.detail2': 'Customer sentiment judgement by AI;',
  'home.page.text.aigc.detail3':
    'Generate images in different styles based on text prompts.',
  'home.page.text.channel':
    'Integrate channels for a seamless customer experience',
  'home.page.text.channel.detail':
    'ConnectNow <b>{US}</b> channels such as phone, Facebook, WhatsApp, and email, allowing customers to connect with you through their preferred method and enjoy a consistent service experience.',
  'home.page.text.channel.span': 'Seamless integration',
  'home.page.text.channel.detail1':
    'Enable customers to contact you through their preferred methods and enjoy a consistent service experience.',
  'home.page.text.channel.info': 'All-Channel 360° Customer Information',
  'home.page.text.channel.info.detail':
    '<b>{US1}</b> of customer information across <b>{US2}</b> such as phone, Facebook, WhatsApp, email, and other social media platforms;',
  'home.page.text.channel.info.detail1':
    'View history of conversations across <b>{US}</b>;',
  'home.page.text.channel.info.detail2': 'Historical dialogue records of;',
  'home.page.text.channel.info.detail3':
    'Configure additional custom fields for customer profiles based on your specific needs.',
  'home.page.text.channel.info.span': 'Centralized display',
  'home.page.text.channel.info.span1': 'all channels',
  'home.page.text.intelligent':
    'Intelligent tickets system, automatically record each customer interaction',
  'home.page.text.intelligent.detail':
    'Customer inquiries from any channel will be recorded and <b>{US1}</b> into <b>{US2}</b> for easy follow-up and transfer, ensuring customers receive the best feedback.',
  // 'home.page.text.intelligent.span': 'any channel',
  // 'home.page.text.intelligent.detail1':
  //   'Customer inquiries will be recorded and automatically generated into work orders for easy follow-up and transfer, ensuring that customers receive the best feedback.',
  'home.page.text.intelligent.customer.service':
    'Intelligent customer service chatbot',
  'home.page.text.intelligent.customer.service.detail':
    '<b>{CH}</b> online gold medal chatbot customer service, round-the-clock automatic reply to customer high-frequency and repetitive questions, support learning enterprise various documents, and set standard FAQs, built-in NLP, built-in NLP natural language understanding, giving customers precise, easy to understand, professional automatic replies.',
  'home.page.text.teamwork': 'Team Collaboration',
  'home.page.text.teamwork.detail':
    'ConnectNow allows customer service to directly transfer ongoing calls or chats to other colleagues internally for <b>{US}</b>.',
  'home.page.text.teamwork.detail1':
    ', The platform allows customer service personnel to transfer the current call or chat directly to other customer service personnel.',
  'home.page.text.teamwork.span': 'collaborate with',
  'home.page.text.convenient': 'Multidimensional data BI reports',
  'home.page.text.convenient.detail':
    'Contains <b>{US}</b> built-in monitoring reports to display customer service operations from multiple perspectives in real-time.',
  'home.page.text.global': 'Worldwide deployment, smooth and stable',
  'home.page.text.global.detail':
    'Supports deployment in <b>{US}</b> worldwide, with smooth connections and stable calls, listening to every voice of your customers.',
  'home.page.text.fast': 'Rapid deployment, seamless upgrades',
  'home.page.text.fast.detail':
    'ConnectNow built within <b>{US}</b>, enabling fast and seamless launch.',
  'home.page.text.start.tips':
    'Are you ready to elevate your customer service experience?',
  'home.page.text.start.btn': 'Get started!',
  'home.page.footer.service.consultation': 'Service Consultation',
  'home.page.footer.service.phone': 'Service hotline: 010-********',
  'home.page.footer.service.email': 'Contact email: <EMAIL>',
  'home.page.footer.service.qq': 'QQ contact: **********',
  'home.page.footer.about.us': 'About Us',
  'home.page.footer.contact.us': 'Contact Us',
  'home.page.footer.friendly.links': 'Friendship Links',
  'home.page.footer.friendly.links.goclouds': 'Goclouds Data',
  'home.page.footer.follow.us': 'Follow Us',
  'home.page.footer.follow.item1': 'Follow Official Account',
  'home.page.footer.follow.item2': 'Follow Official Mini Program',
  // 文本
  'home.page.contactUS.test.contactAddress': 'Contact Address',
  'home.page.contactUS.text.address':
    'Heqiao Building, No.8 Guanghua Road, Chaoyang District, Beijing',
  'home.page.contactUS.text.emailAndPhone': 'Email',
  'home.page.contactUS.text.socialMedia': 'Social Media',
  // 表单
  'home.page.contactUS.form.lable.userName': 'Name',
  'home.page.contactUS.form.lable.companyEmail': 'Email',
  'home.page.contactUS.form.lable.phoneNumber': 'Phone',
  'home.page.contactUS.form.lable.question': 'Question',
  'home.page.contactUS.form.placeholder.lastName': 'Enter your last name here',
  'home.page.contactUS.form.placeholder.userName': 'Enter your first name here',
  'home.page.contactUS.form.placeholder.companyEmail':
    'Enter your company email here',
  'home.page.contactUS.form.placeholder.phoneNumber': 'Enter your phone here',
  'home.page.contactUS.form.placeholder.question': 'Enter your question here.',
  'home.page.contactUS.form.placeholder.lastName1':
    'Please enter your last name',
  'home.page.contactUS.form.placeholder.userName1':
    'Please enter your first name',
  'home.page.contactUS.form.placeholder.companyEmail1':
    'Please enter your email',
  'home.page.contactUS.form.placeholder.phoneNumber1':
    'Please enter your phone',
  // 按钮
  'home.page.contactUS.button.submit': 'Submit',
  'home.page.center.content.title': 'Features',
  'login.tips.title': 'CoCo reminds you',
  'login.tips.text':
    'Dear, please log in and experience using a computer browser~',
  'login.tips.return.homePage.btn': 'Homepage',
  'phone.header.select.language': 'Select Language',
  'home.page.region.china': 'China',
  'home.page.region.asia': 'Asia / Pacific',
  'home.page.region.europe': 'Europe',
  'home.page.region.usa': 'USA',
};
