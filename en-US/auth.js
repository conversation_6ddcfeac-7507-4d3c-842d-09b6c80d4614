// export default {
//   // 登录
//   'login.user.email.input': 'Please input your email',
//   'login.user.email.pattern': 'Must conform to the mailbox format',
//   'login.user.password.input': 'Please input your password',
//   'login.user.captcha.input': 'Please input captcha',
//   'login.user.captcha.error.input':
//     'The entered verification code is incorrect',
//   'login.remember': 'Remember password',
//   'login.button': 'Login',
//   'login.success': 'Login successfully',
//   'login.title': 'User Login',
//   'login.email': 'User Email',
//   'login.pwd': 'User Password',
//
//   // 注册
//   'program.title': 'Management Platform',
//   'register.email': 'Email',
//   'register.captcha': 'Captcha',
//   'register.phone': 'Phone Number',
//   'register.user.name': 'Name',
//   'register.user.post': 'Post',
//   'register.user.password': 'Initial password',
//   'register.deptName': 'Organization name',
//   'register.deptId': 'Organization number',
//   'register.parentDept': 'Superior organization',
//   'register.deptPerson': 'Head of organization',
//   'register.company.name': 'Company Name',
//   'register.company.code': 'Company Code',
//   'register.user.invite': 'Invitation code',
//   'register.user.invite.input': 'Please enter the invitation code',
//   'register.company.address': 'Company Address',
//   'register.email.notice':
//     'Note: The mailbox will be used for subsequent login. Please ensure the validity of the mailbox',
//   'register.email.captcha': 'Get Code',
//   'register.user.workNumber': 'Job number',
//   'register.user.name.input.workNumber': 'Please enter the job number',
//   'register.email.captcha.second': '{captchaCount} seconds',
//   'register.user.name.input.initialPassword':
//     'Please enter the initial password',
//   'register.user.name.input': 'Please input your name',
//   'register.user.phone.input': 'Please input your phone number',
//   'register.user.phone.length.input':
//     'Must conform to the format of the mobile phone number',
//   'register.user.pwd.length.input':
//     'Please enter a password of 6-18 characters',
//   'register.user.post.input': 'Please input your post',
//   'register.user.post.length.input':
//     'The length of a post cannot exceed 80 characters',
//   'register.company.name.input': 'Please input your company name',
//   'register.company.name.length.input':
//     'The length of the company name cannot exceed 80 characters',
//   'register.company.code.input': 'Please input your company code',
//   'register.company.code.length.input':
//     'The length of the company code cannot exceed 80 characters',
//   'register.company.address.input': 'Please input your company address',
//   'register.company.address.length.input':
//     'The length of the company address cannot exceed 2000 characters',
//   'register.user.service.term':
//     "Please read the customer's terms of service first",
//   'register.remember': 'I have read it carefully',
//   'register.button': 'Register',
//   'register.no.account': 'No account？',
//   'register.now.register': 'register immediately',
//   'register.success': 'Register Successfully',
//   'register.service.tips': 'Cloud potential data user Terms of service',
//
//   logout: 'Quit',
//   'layout.setting.aws.account': 'AWS Account Setting',
//   'layout.setting.channel': 'Channel allocation',
//   'layout.setting.system': 'System allocation',
//   'layout.setting.customer': 'Customer extended information configuration',
//   'layout.setting.work.order': 'Configure work order extension information',
//   'layout.worktable': 'worktable',
// };
export default {
  // Sign in
  'login.user.email.input': 'Please enter your email',
  'login.user.email.pattern': 'Must comply with email format',
  'login.user.password.input': 'Please enter your password',
  'login.user.captcha.input': 'Please enter your verification code',
  'login.user.captcha.input1': 'Please drag the slider to verify',
  'login.user.captcha.error.input':
    'The verification code entered is incorrect',
  'login.remember': 'Remember password',
  'login.button': 'Login',
  'login.success': 'Login succeeded',
  'login.title': 'User Login',
  'login.email': 'User email',
  'login.pwd': 'User password',
  // Sign up
  'register.footer.text':
    "By creating an account, you agree to ConnectNow's {link} and {link2}",
  'register.footer.text.marketing':
    'I consent to receiving marketing communications and can withdraw my consent anytime.',
  'register.footer.link': '《User Agreement》',
  'register.footer.link2': '《ConnectNow Privacy Policy》',
  'register.login.now': 'Already have an account？',
  'register.login.now.link': 'Login now',
  'register.title': 'Sign up for',
  'program.title': 'Management platform',
  'register.email': 'Email',
  'register.deptName': 'Team',
  'register.deptId': 'Code',
  'register.parentDept': 'Parent',
  'register.deptPerson': 'Manager',
  'register.captcha': 'Verification code',
  'register.phone': 'Phone',
  'register.user.password': 'Initial password',
  'register.user.invite': 'Invitation code',
  'register.user.invite.input': 'Please enter invitation code',
  'register.last.name': 'Last name:',
  'register.user.name': 'First name:',
  'register.user.workNumber': 'Employee ID',
  'register.user.post': 'Job title',
  'register.company.name': 'Company name',
  'register.company.code': 'Company code',
  'register.company.address': 'Company address',
  'register.email.notice':
    'Note: The email will be used for subsequent logins, please ensure the email is valid',
  'register.email.captcha': 'Get Code',
  'register.email.captcha.second': '{captchaCount} Seconds',
  'register.last.name.input': 'Please enter your last name',
  'register.user.name.input': 'Please enter your first name',
  'register.user.name.input.initialPassword':
    'Please enter your initial password',
  'register.user.name.input.workNumber': 'Please enter your employee ID',
  'register.user.phone.input': 'Please enter your phone',
  'register.user.phone.length.input': 'Must conform to the phone number format',
  'register.user.post.input': 'Please enter your job title',
  'register.user.post.length.input':
    'Position length cannot exceed 80 characters',
  'register.user.pwd.length.input':
    'Please enter a password with a length of 6-18 characters',
  'register.company.name.input': 'Please enter your company name',
  'register.company.name.length.input':
    'The length of the company name cannot exceed 80 characters',
  'register.company.code.input': 'Please enter your company code',
  'register.company.code.length.input':
    'The length of the company code cannot exceed 80 characters',
  'register.company.address.input': 'Please enter your company address',
  'register.company.address.length.input':
    'The length of the company address cannot exceed 2000 characters',
  'register.user.service.term': 'Please read the User Service Terms',
  'register.remember': "By creating an account, you agree to Goclouds Data's ",
  'register.no.account': "Don't have an account?",
  'register.now.register': ' Sign up now',
  'register.button': 'Sign up',
  'register.success': 'Sign up succeeded',
  'register.success.modal':
    'You have successfully registered. We will review your registration within 2-3 business days. Once approved, you will receive an email notification. Please check your inbox.',
  'register.service.tips': '《User Service Terms》',
  logout: 'Logout',
  'layout.setting.aws.account': 'AWS account configuration',
  'layout.setting.channel': 'Channels configuration',
  'layout.setting.system': 'System configuration;',
  'layout.setting.customer': 'Customer extension information configuration',
  'layout.setting.work.order': 'Tickets extension information configuration',
  'layout.worktable': 'Workspace',
  'common.unknown.error.tips': 'Unknown exception, please try again later',
  'common.unknown.error.kickout.tips':
    'Your account has been logged in elsewhere',
  'common.unknown.error.timeout.tips':
    'Request timed out, please try again later',
  'register.captcha.operation.tips': 'Drag to the right to fill in the puzzle',
  'connectNow.title': 'AI-Native Customer Service Experts',
  'auth.access.no': 'Insufficient permissions',
  'auth.access.no.1': 'Your current version does not support this feature',
};
