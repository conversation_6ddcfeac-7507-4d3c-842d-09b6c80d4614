export default {
  'selfConfiguration.title': 'AIGC Chatbot Evaluation Configuration',
  'selfConfiguration.label.1': 'Enable system automatic sampling evaluation:',
  'selfConfiguration.label.1.tips':
    'If self-evaluation is enabled, the system will conduct sample checks based on your subsequent settings and provide an automated accuracy evaluation report.',
  'selfConfiguration.label.2':
    'Set the number of questions to sample and evaluate each day',
  'selfConfiguration.label.2.value': 'Perform an automatic evaluation every',
  'selfConfiguration.label.2.value.1': 'questions',
  'selfConfiguration.label.2.value.2': 'and',
  'selfConfiguration.label.2.value.3': 'UP to',
  'selfConfiguration.label.2.value.4':
    'questions can be extracted for automatic evaluation each day',
  'selfConfiguration.label.3': 'Frequency of self-evaluation',
  'selfConfiguration.label.4': ' ',
  'selfConfiguration.label.tips': 'Please select time',
  'selfReport.title': 'AIGC Chatbot Evaluation Report',
  'selfReport.label.card.1': 'Average accuracy score',
  'selfReport.label.card.2': 'Average accuracy score in each dimension',
  'selfReport.label.card.3': 'Average accuracy score in each dimension',
  'selfReport.label.card.4': 'Self-evaluation accuracy score trend chart',
  'selfReport.label.card.5':
    'Self-evaluation accuracy score trend chart by dimension',
  'selfReport.fen': 'points',
  'selfReport.label.1': 'Closeness to the Facts Score',
  'selfReport.label.2': 'Answer Relevance Score',
  'selfReport.label.3': 'Context Precision Score',
  'selfReport.label.4': 'Answer Semantic Similarity Score',
  'selfReport.label.5': 'Answer Correctness Score',
  'selfReport.select.knowledge': 'Knowledge base: ',
  'selfReport.popover.1': `This measures the factual consistency of the generated answer against the given context. It is calculated from answer and retrieved context. The answer is scaled to (0,100) range. Higher the better.`,
  'selfReport.popover.1.1':
    'The generated answer is regarded as faithful if all the claims made in the answer can be inferred from the given context.',
  'selfReport.popover.2': `The evaluation metric, Answer Relevancy, focuses on assessing how pertinent the generated answer is to the given prompt. A lower score is assigned to answers that are incomplete or contain redundant information and higher scores indicate better relevancy. This metric is computed using the question, the context and the answer.`,
  'selfReport.popover.3': `Context Precision is a metric that evaluates whether all of the ground-truth relevant items present in the contexts are ranked higher or not. Ideally all the relevant chunks must appear at the top ranks. This metric is computed using the question, ground_truth and the contexts, with values ranging between 0 and 1, where higher scores indicate better precision.`,
  'selfReport.popover.4': `The concept of Answer Semantic Similarity pertains to the assessment of the semantic resemblance between the generated answer and the ground truth. This evaluation is based on the ground truth and the answer, with values falling within the range of 0 to 1. A higher score signifies a better alignment between the generated answer and the ground truth.`,
  'selfReport.popover.5': `The assessment of Answer Correctness involves gauging the accuracy of the generated answer when compared to the ground truth. This evaluation relies on the ground truth and the answer, with scores ranging from 0 to 1. A higher score indicates a closer alignment between the generated answer and the ground truth, signifying better correctness.`,
  'selfReport.popover.5.5': `Answer correctness encompasses two critical aspects: semantic similarity between the generated answer and the ground truth, as well as factual similarity. These aspects are combined using a weighted scheme to formulate the answer correctness score.`,
};
