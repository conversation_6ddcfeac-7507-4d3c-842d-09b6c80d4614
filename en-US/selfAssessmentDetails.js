export default {
  // 评估报告列表
  'self.assessment.details.select.knowledge': 'Knowledge base: ',
  'self.assessment.details.select.knowledge.placeholder':
    'Please select the knowledge base',
  'self.assessment.details.notification.channels': 'Source channel: ',
  'self.assessment.details.notification.channels.placeholder':
    'Please select the source channel',
  'self.assessment.details.ask.customer': 'Customer who asks the question: ',
  'self.assessment.details.ask.customer.placeholder':
    'Please enter the customer name, customer email, or customer phone number',
  'self.assessment.details.ask.customer.placeholder1':
    'Please enter the customer email',
  'self.assessment.details.ask.customer.placeholder.phone':
    'Please enter the customer phone',
  'self.assessment.details.customer.issues': "Customer's question: ",
  'self.assessment.details.customer.issues.placeholder':
    'Please enter your question',
  'self.assessment.details.question.time': 'Question time: ',
  'self.assessment.details.title': 'List of Evaluation Reports',
  'self.assessment.details.table.assessment.type': 'Evaluation types',
  'self.assessment.details.table.manual.evaluation': 'Manual',
  'self.assessment.details.table.automatic.evaluation': 'Automatic',
  'self.assessment.details.table.customer.issues': "Customer's question",
  'self.assessment.details.table.answer.obtained': "AI Chatbot's answer",
  'self.assessment.details.table.average.accuracy.score':
    'Average accuracy score',
  'self.assessment.details.table.loyalty.score': 'Closeness to the Facts Score',
  'self.assessment.details.table.answer.relevance.score':
    'Answer Relevance Score',
  'self.assessment.details.table.context.accuracy.score':
    'Context Precision Score',
  'self.assessment.details.table.semantic.similarity.score':
    'Answer Semantic Similarity Score',
  'self.assessment.details.table.correct.answer.score':
    'Answer Correctness Score',
  'self.assessment.details.table.ask.customer': 'Customer',
  'self.assessment.details.table.interaction.time': 'Question time',
  'self.assessment.details.table.knowledge.name': 'Knowledge base name',
  'self.assessment.details.table.loyalty.score.tips.text':
    'This measures the factual consistency of the generated answer against the given context. It is calculated from answer and retrieved context. The answer is scaled to (0,100) range. Higher the better.',
  'self.assessment.details.table.loyalty.score.tips.text.1':
    'The generated answer is regarded as faithful if all the claims made in the answer can be inferred from the given context.',
  'self.assessment.details.table.answer.relevance.score.tips.text':
    'The evaluation metric, Answer Relevancy, focuses on assessing how pertinent the generated answer is to the given prompt. A lower score is assigned to answers that are incomplete or contain redundant information and higher scores indicate better relevancy. This metric is computed using the question, the context and the answer.',
  'self.assessment.details.table.context.accuracy.score.tips.text':
    'Context Precision is a metric that evaluates whether all of the ground-truth relevant items present in the contexts are ranked higher or not. Ideally all the relevant chunks must appear at the top ranks. This metric is computed using the question, ground_truth and the contexts, with values ranging between 0 and 100, where higher scores indicate better precision.',
  'self.assessment.details.table.semantic.similarity.score.tips.text':
    'The concept of Answer Semantic Similarity pertains to the evaluation of the semantic resemblance between the generated answer and the ground truth. This evaluation is based on the ground truth and the answer, with values falling within the range of 0 to 100. A higher score signifies a better alignment between the generated answer and the ground truth.',
  'self.assessment.details.table.correct.answer.score.tips.text':
    'The assessment of Answer Correctness involves gauging the accuracy of the generated answer when compared to the ground truth. This evaluation relies on the ground truth and the answer, with scores ranging from 0 to 100. A higher score indicates a closer alignment between the generated answer and the ground truth, signifying better correctness.',
  'self.assessment.details.table.correct.answer.score.tips.text.1':
    'Answer correctness encompasses two critical aspects: semantic similarity between the generated answer and the ground truth, as well as factual similarity. These aspects are combined using a weighted scheme to formulate the answer correctness score.',
  // 评估报告详情
  'self.assessment.details.title.left': 'Question',
  'self.assessment.details.title.right': 'Evaluation score',
  'self.assessment.details.evaluation.time': 'Evaluation time: ',
  'self.assessment.details.comparison.answers': 'Answer comparison',
  'self.assessment.details.document.fragments': 'Original document chunk',
  'self.assessment.details.robot.answer': "AI Chatbot's answer",
  'self.assessment.details.correct.answer': 'Correct answer',
  'self.assessment.details.add.faq': 'Add to FAQ',
  'self.assessment.details.document.knowledge': 'AlGC knowledge base',
  'self.assessment.details.input.correct.answer':
    'Manually input the correct answer',
  'self.assessment.details.create.correct.answer.text':
    'After manually entering the correct answer, the system can evaluate the values of "Context precision", "Answer semantic similarity", and "Answer correctness" for you.',
  'self.assessment.details.input.correct.answer.placeholder':
    'Please enter the correct answer.',
  'self.assessment.details.start.evaluating': 'Start evaluation',
  'self.assessment.details.cancel.evaluation': 'Cancel evaluation',
  'self.assessment.details.loyalty': 'Closeness to the Facts',
  'self.assessment.details.answer.relevance': 'Answer relevancy',
  'self.assessment.details.context.accuracy': 'Context precision',
  'self.assessment.details.semantic.similarity.answers': 'Answer similarity',
  'self.assessment.details.correct.answer.radar': 'Answer correctness',
  'self.assessment.details.handle.input.text':
    'After manually entering the correct answer, the system can evaluate the values of "Context precision", "Answer semantic similarity", and "Answer correctness" for you.',
  'self.assessment.details.title.right.evaluation.status':
    'Under evaluation...',
  // 智能客服对话洞察
  'self.assessment.details.interactivity.type': 'Interaction type: ',
  'customer.service.conversation.insights.answer.type.select': 'Answer type: ',
  'customer.service.conversation.insights.answer.type.select.placeholder':
    'Please select the answer type',
  'customer.service.conversation.insights.like': 'Like',
  'customer.service.conversation.insights.downvote': 'Dislike',
  'self.assessment.details.table.interactivity.type': 'Interaction type',
  'self.assessment.details.document.fragments.faq': 'FAQ answers',
  'self.assessment.details.document.fragments.answer': 'Answer',
  'customer.service.conversation.insights.no.data.tips':
    'Lacking relevant knowledge, please maintain the knowledge base',
  'customer.service.conversation.insights.no.data.tips.1':
    'You currently do not have any data information...',
  'customer.service.conversation.insights.title':
    'AlGC Chatbot Conversation Insights',
  'customer.service.conversation.insights.from.faq':
    'From the FAQ knowledge base',
  'customer.service.conversation.insights.input.correct.answer':
    'Enter the correct answer',
  'customer.service.conversation.insights.answer.type': 'Answer type',
  'customer.service.conversation.insights.table.knowledge.QA':
    'FAQ Normal Answer',
  'customer.service.conversation.insights.table.lack.knowledge':
    'RAG Lacks Knowledge',
  'customer.service.conversation.insights.table.ai.dont.know': 'RAG Unknown',
  'customer.service.conversation.insights.table.aigc.answer':
    'RAG Normal Answer',
  'customer.service.conversation.insights.table.aigc.answer.aigc':
    'AIGC Normal Answer',
  'customer.service.conversation.insights.title.right': 'Evaluate accuracy',
  'customer.service.conversation.insights.detail.answer.null':
    'No answers found yet.',
  'self.assessment.details.title.problem.rewriting': 'Question rewrite: ',
  // 智能客服热点问题分析
  'customer.service.hot.topic.analysis.title':
    'AIGC Chatbot Hot Inquiries Analysis',
  'customer.service.hot.topic.analysis.top.20': 'Top 20 word cloud',
  'customer.service.hot.topic.analysis.top.100': 'Top 100 word cloud',
  'customer.service.hot.topic.analysis.search.tips':
    'Please enter tags to search',
  'customer.service.hot.topic.analysis.table.question.tag': 'Word cloud',
  'customer.service.hot.topic.analysis.top.10':
    'Top 10 Most Frequently Asked Questions',
  'customer.service.hot.topic.analysis.table.ranking': 'Ranking',
  'customer.service.hot.topic.analysis.table.ask.questions': 'Ask questions',
  'customer.service.hot.topic.analysis.table.ask.number':
    'Number of questions asked',
  'customer.service.hot.topic.analysis.table.like.number': 'Number of likes',
  'customer.service.hot.topic.analysis.table.dislike.number':
    'Number of dislikes',
  'customer.service.hot.topic.analysis.table.recent.question.time':
    'Recent question time',
  'customer.service.hot.topic.analysis.top.20.tips':
    'Displays the top 20 most frequently asked word cloud. These tags provide a quick overview of the main areas of user concern.',
  'customer.service.hot.topic.analysis.top.100.tops':
    'Displays the top 100 most frequently asked word cloud. By clicking on a tag, you can retrieve detailed data about questions under that tag. Understanding these tags helps identify the issues users are most focused on and provides data support for optimizing the knowledge base and improving customer service.',
  'customer.service.hot.topic.analysis.top.10.tips':
    'Shows the 10 questions most frequently asked by users. The high frequency of these questions indicates they are topics of greatest concern to users.',
  // 缺乏知识问题分析
  'lack.knowledge.problem.analysis.title':
    'Lack of Knowledge Inquiries Analysis',
  'lack.knowledge.problem.analysis.top.20':
    'Top 20 word cloud due to lack of knowledge',
  'lack.knowledge.problem.analysis.top.100':
    'Top 100 word cloud lacking knowledge',
  'lack.knowledge.problem.analysis.bottom.title':
    'Questions answered as "I don\'t know" due to lack of knowledge',
  'lack.knowledge.problem.analysis.top.20.tips':
    'Displays the top 20 word cloud that resulted in "lack of knowledge" responses after customer inquiries. These tags represent areas where users frequently encounter issues, indicating an urgent need to supplement and improve the knowledge base content.',
  'lack.knowledge.problem.analysis.top.100.tips':
    'Displays the top 100 word cloud that resulted in "lack of knowledge" responses after customer inquiries. Understanding these tags helps identify the issues users are most focused on and provides data support for optimizing the knowledge base and improving customer service.',
  'lack.knowledge.problem.analysis.bottom.title.tips':
    'Shows all questions that received a "lack of knowledge" response.',
};
