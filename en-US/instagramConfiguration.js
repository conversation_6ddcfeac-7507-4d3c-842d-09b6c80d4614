export default {
  'add.Instagram.channel.configuration.title': 'Add Instagram',
  'add.Instagram.channel.configuration.title.update': 'Update Instagram',
  'add.Instagram.channel.configuration.tips':
    'Simplify customer interactions via Instagram messages, enabling faster and more effective responses to their inquiries.',
  'Instagram.channel.configuration.title.1': 'Bind Instagram account',
  'Instagram.channel.configuration.title.1.p':
    'Please bind your Instagram account first',
  'Instagram.channel.configuration.title.tips.1':
    'Simplify customer interactions via Instagram messages, enabling faster and more effective responses to their inquiries.',
  'Instagram.channel.configuration.title.1.go': 'Bind Instagram account',
  'Instagram.channel.configuration.title.1.go.finish':
    'You have successfully bound your Instagram account, please continue to select your Instagram account.',
  'Instagram.channel.configuration.1.finish': 'Selection completed',
  'Instagram.channel.configuration.title.2': 'Select Instagram account',
  'Instagram.channel.configuration.title.tips.2':
    'Please select the account for subsequent contact with the customer. You can only select one public page. If you need multiple accounts, please configure multiple channels.',
  'Instagram.channel.configuration.title.2.success':
    'You have successfully linked your Instagram account.',
  'Instagram.channel.configuration.title.3':
    'Intelligent Customer Service Settings',
  'Instagram.channel.configuration.title.tips.3':
    'You can configure the relevant information of the intelligent customer service here.',
  'Instagram.channel.configuration.title.3.qingxu': 'Robot Emotion',
  'Instagram.channel.configuration.title.3.qingxu.radio.1': 'Professional Mode',
  'Instagram.channel.configuration.title.3.qingxu.radio.2':
    'Emotionally Intelligent Mode (Softer Responses)',
  'Instagram.channel.configuration.title.3.qingxu.radio.tips':
    'By enabling "Emotionally Intelligent Mode", the AlGC Chatbot will deliver softer responses to your customers.',
  'Instagram.channel.configuration.title.4': 'Adding channel information',
  'Instagram.channel.configuration.title.tips.4':
    'Please enter channel information',
};
