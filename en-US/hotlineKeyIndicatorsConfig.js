export default {
  'hotlineKeyIndicatorsConfig.title':
    'Hotline KeyMetrics Dashboard Rule Config',
  'hotlineKeyIndicatorsConfig.setEmail': 'Set email notifications',
  'hotlineKeyIndicatorsConfig.IVR.title': 'IVR queue volume',
  'hotlineKeyIndicatorsConfig.IVR.queue': 'IVR queue: ',
  'hotlineKeyIndicatorsConfig.IVR.queueTip':
    'Current queue / Current online agents',
  'hotlineKeyIndicatorsConfig.IVR.inbound': 'Avg. inbound call queuing time',
  'hotlineKeyIndicatorsConfig.IVR.inboundTime':
    'When the average inbound queuing time is greater than',
  'hotlineKeyIndicatorsConfig.IVR.inboundSecond': 'seconds, display as ',
  'hotlineKeyIndicatorsConfig.IVR.inboundSecondOrange': 'orange',
  'hotlineKeyIndicatorsConfig.IVR.inboundSecondRed': 'red',
  'hotlineKeyIndicatorsConfig.inbound.count': 'Inbound call volume',
  'hotlineKeyIndicatorsConfig.inbound.countIncrease': ' Inbound call surge',
  'hotlineKeyIndicatorsConfig.inbound.countIncrease24H':
    'Inbound call volume in the last 24 hours surged compared to the previous 24 hours',
  'hotlineKeyIndicatorsConfig.inbound.countShow': '%, display as',
  'hotlineKeyIndicatorsConfig.connectionRate.title': 'Connection rate',
  'hotlineKeyIndicatorsConfig.connectionRate.total': 'Total connection rate',
  'hotlineKeyIndicatorsConfig.connectionRate.inbound':
    'Inbound connection rate',
  'hotlineKeyIndicatorsConfig.connectionRate.serviceInbound':
    'Service time inbound connection rate',
  'hotlineKeyIndicatorsConfig.connectionRate.Outbound':
    'Outbound connection rate',
  'hotlineKeyIndicatorsConfig.AWC.title': 'AWC time',
  'hotlineKeyIndicatorsConfig.AWC.inbound': 'Avg. inbound AWC time',
  'hotlineKeyIndicatorsConfig.AWC.outbound': 'Avg. outbound AWC time',
  'hotlineKeyIndicatorsConfig.AWC.inboundTime':
    'When the average inbound ACW time is greater than',
  'hotlineKeyIndicatorsConfig.AWC.outboundTime':
    'When the average outbound ACW time is greater than',
  'hotlineKeyIndicatorsConfig.abandonment.title': 'Abandonment rate',
  'hotlineKeyIndicatorsConfig.abandonment.total': 'Total abandonment rate',
  'hotlineKeyIndicatorsConfig.abandonment.unreachable': 'Unanswered call rate',
  'hotlineKeyIndicatorsConfig.abandonment.IVR': 'IVR abandonment rate',
  'hotlineKeyIndicatorsConfig.abandonment.abandon': 'Queue abandonment rate',
  'hotlineKeyIndicatorsConfig.abandonment.inbound':
    'After-Hours inbound call rate',
  'hotlineKeyIndicatorsConfig.satisfaction.title': 'Satisfaction score',
  'hotlineKeyIndicatorsConfig.satisfaction.average': 'Avg. satisfaction score',
  'hotlineKeyIndicatorsConfig.satisfaction.inbound':
    'Avg. inbound satisfaction score',
  'hotlineKeyIndicatorsConfig.satisfaction.outbound':
    'Avg. outbound satisfaction score',
  'hotlineKeyIndicatorsConfig.other.title': 'Other configurations',
  'hotlineKeyIndicatorsConfig.other.inbound': 'Inbound call transfer rate',
  'hotlineKeyIndicatorsConfig.other.outbound': 'Outbound call transfer rate',
  'hotlineKeyIndicatorsConfig.other.inboundHangUp':
    'Inbound agent hang-up rate',
  'hotlineKeyIndicatorsConfig.other.outboundHangUp':
    'Outbound agent hang-up rate',
  'hotlineKeyIndicatorsConfig.other.repeatInbound': 'Repeat call rate',
  'hotlineKeyIndicatorsConfig.btn.save': 'Save',
  'hotlineKeyIndicatorsConfig.btn.cancel': 'Cancel',
  'hotlineKeyIndicatorsConfig.btn.saveErr':
    'Invalid input, please check your input',
  'hotlineKeyIndicatorsConfig.modal.title': 'Hotline metrics email alert',
  'hotlineKeyIndicatorsConfig.modal.text1': 'When the hotline metrics',
  'hotlineKeyIndicatorsConfig.modal.tipText1': ' triggers an alert',
  'hotlineKeyIndicatorsConfig.modal.text2':
    ' , the following platform users will be sent ',
  'hotlineKeyIndicatorsConfig.modal.tipText2': ' email notifications.',
  'hotlineKeyIndicatorsConfig.modal.tip':
    'Note: The system will automatically check every hour.',
  'hotlineKeyIndicatorsConfig.modal.user': 'Notify users',
  'hotlineKeyIndicatorsConfig.operation.success': 'Notify users',
  'hotlineKeyIndicatorsConfig.operation.success.placeholder':
    'Please select notify users',
  'hotlineKeyIndicatorsConfig.times.text': ' times',
};
