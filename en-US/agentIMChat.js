export default {
  'im.agent.status.available': 'Available',
  'im.agent.status.offline': 'Offline',
  'im.agent.status.reconnect': 'Reconnect',
  'im.queue.wait.count':
    'There are currently {waitCount} people in the queue...',
  'im.chat.none': 'You currently have no unread messages',
  'im.chat.accept': 'Accept chat',
  'im.chat.reject': 'Reject chat',
  'im.chat.join': '{joinChat} joined the chat',
  'im.chat.join.btn': 'Join the chat',
  'im.message.read': 'Read',
  'im.message.unread': 'Unread',
  'im.message.enter.prompt': 'Press Enter to send your message',
  'im.chat.end': 'End chat',
  'im.chat.acw.open':
    'You are currently in {acw} status. During this time, you can summarize and organize the chat as needed. Once finished, please click the "Close Chat" button below',
  'im.chat.acw.close': 'Close chat',
  'im.chat.acw.finish':
    'You have ended the online chat with the customer, please continue with the follow-up work',
  'im.chat.acw.timeout':
    'Your current conversation has exceeded 24 hours and you cannot directly communicate with the current customer. However, you can contact the customer by sending WhatsApp marketing templates.',
  'im.chat.acw.timeout.public':
    'Your current conversation has exceeded {time} hours and you cannot directly communicate with the current customer.',
  'im.chat.acw.prompt': 'After contact work',
  'im.chat.left': '{leftChat} left the chat',
  'im.chat.uploading': 'Uploading',
  'im.chat.rating': 'The satisfaction rating has been submitted',
  'im.chat.evaluation': 'Customer Satisfaction Evaluation',
  'im.chat.nodata': 'You currently have no unread messages...',
  'im.chat.miss': "You've missed {userName} chat",
  'im.chat.translation.title': 'Auto Translation',
  'im.chat.history.title': 'Auto Translation',
  'im.chat.translation.content': 'Enable Auto Translation',
  'im.chat.translation.retry': 'Retry',
  'im.chat.translation.retry.re':
    'Network connection error, attempting to reconnect...',
  'im.chat.translation.retry.re.alert':
    'The current connection has been disconnected. Please refresh the page and try again.',
  'im.chat.collect.ticket':
    'Please click the assign button on the left to start communication with the current customer',
  'im.chat.allocation.ticket':
    'Click the "Assign Work Order" button on the left to assign the ticket to the agent',
};
