export default {
  'ticket.setting.title': 'Ticket settings',
  'ticket.setting.menu.1': 'Ticket and customer management',
  'ticket.setting.menu.1.subtitle':
    'Configuration of ticket types, ticket fields, and customer fields',
  'ticket.setting.menu.1.ticket.type': 'Ticket type definition',
  'ticket.setting.menu.1.ticket.attr': 'Ticket attribute definition',
  'ticket.setting.menu.1.customer.attr': 'Customer attribute definition',
  'ticket.setting.menu.1.permission.setting': 'Permission settings',
  'permission.setting.sensitive.information.desensitization.worktable':
    'Agent chat sensitive data masking',
  'permission.setting.sensitive.information.desensitization.tips.worktable':
    'When enabled, user-sent phone numbers, emails, etc. will be automatically masked',
  'permission.setting.sensitive.information.desensitization':
    'Customer profile sensitive data masking',
  'permission.setting.sensitive.information.desensitization.tips':
    'When enabled, user contact information will be automatically masked, e.g., email, mobile number',
  'permission.setting.allow.downloading.audio.files':
    'Allow call recording download',
  'permission.setting.allow.downloading.audio.files.tips':
    'When enabled, call recordings can be downloaded from the ticket details page',
  'ticket.setting.menu.2': 'Agent operations management',
  'ticket.setting.menu.2.subtitle':
    'Configuration of SLA, business hours, and agent statuses',
  'ticket.setting.menu.3': 'Automation configuration',
  'ticket.setting.menu.3.subtitle':
    'Configuration of routing, automation, alerts, and intelligent response',
  'automation.config.response.title.1': 'Automation',
  'automation.config.response.title.2': 'Intelligent response',
  'automation.config.routing.title': 'Routing',
  'automation.config.alert.title': 'Ticket alerts',
  'automation.config.ticket.aigc': 'Ticket AIGC assist',
  'automation.config.smart.ticket': 'Intelligent ticket filling',
  'automation.config.auto.close': 'Auto-close ticket settings',
  'automation.config.merge.tickets': 'Merge ticket settings',
  'automation.config.seat.reply.classification': 'Agent macro categories',
  'automation.config.seat.reply': 'Agent macros',
  'automation.config.inactive.reminder': 'Inactivity notification',
  'automation.config.smart.routing': 'Intelligent routing rules',
  'automation.config.ticket.alert': 'Ticket surge alert',
  'automation.close.ticket.setting': 'Auto-close ticket settings',
  'automation.close.ticket.setting.add.rule': 'New rule',
  'automation.close.ticket.setting.tabs.1': 'Auto-end conversation rule',
  'automation.close.ticket.setting.tabs.2': 'Auto-end ticket rule',
  'automation.close.ticket.setting.table.columns.4':
    'Applicable ticket types for rule',
  'automation.close.ticket.setting.table.columns.content': 'All ticket types',
  'automation.close.ticket.setting.add.rule.title': 'New rule',
  'automation.close.ticket.setting.edit.rule.title': 'Edit rule',
  'automation.close.ticket.setting.add.rule.scope': 'Scope',
  'automation.close.ticket.setting.add.rule.scope.channel': 'Channel',
  'automation.close.ticket.setting.add.rule.scope.ticket.type': 'Ticket type',
  'automation.close.ticket.setting.add.rule.set': 'Rule settings',
  'automation.close.ticket.setting.add.rule.checkBox':
    'Auto-close conversation',
  'automation.close.ticket.setting.add.rule.checkBox.ticket':
    'Auto-close ticket',
  'automation.close.ticket.setting.add.rule.auto.end.session':
    'Auto-close conversation after {slot} of customer inactivity',
  'automation.close.ticket.setting.add.rule.auto.end.ticket':
    'Auto-close ticket if session ended for over {slot}.',
  // 公司基本信息
  'ticket.setting.menu.5': 'Company information',
  'ticket.setting.menu.5.subtitle': 'Set company Logo and name',
  'ticket.setting.menu.5.1': 'Company information',
};
