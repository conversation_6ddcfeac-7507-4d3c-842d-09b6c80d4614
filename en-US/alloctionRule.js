export default {
  'alloctionRule.table.editor': 'Update',
  'alloctionRule.table.detail': 'Detail',
  'alloctionRule.table.delete': 'Delete',
  'alloctionRule.operation.operation': 'Operation',
  'channel.type': 'Channel type',
  'channel.name': 'Channel name',
  'allocationRule.channel.add.rule': 'New intelligent assignment rule',
  'allocationRule.channel.edit.rule': 'Modify intelligent assignment rule',
  'allocationRule.channel.type':
    'Please select the channel to which the current rule applies',
  'allocationRule.channel.name': 'Please select the channel name',
  'allocationRule.judgment.rules': 'Condition rule {number}',
  'allocationRule.any.condition': 'Any condition',
  'allocationRule.personalized.configuration': 'Personalized configuration',
  'allocation.sticky.distribution': 'Sticky assignment',
  'allocation.when.the.system.assigns.tickets.it.will.try.to.route.them.to.the.agent.that.served.the.current.customer.last.time':
    'When the system assigns tickets, it will try to route them to the agent that served the current customer last time',
  'allocation.please.select.customer.tag': 'Please select customer tag',
  'allocation.intelligent.rules': 'Assignment Rules',
  'allocation.more.rules.index.and.back': '" ',
  'allocation.adjust.order': 'Adjust order',
  'allocation.create.a.new.matching.rule': 'Create a new rule',
  'allocation.execution.order': 'Order',
  'allocation.applicable.channel.types.for.rules': 'Channel',
  'allocation.applicable.channel.types.for.default': 'Default',
  'allocation.rule.details': 'Rule',
  'allocation.assign.to.specific.team.maohao.index':
    'assign to specific team: ',
  'allocation.assign.to.specific.agent.maohao.index':
    'assign to specific agent: ',
  'allocation.assign.to.specific.team': 'Assign to specific team',
  'allocation.assign.to.specific.agent': 'Assign to specific agent',
  'allocation.assign.to.specific.team.maohao': 'Assign to specific team: ',
  'allocation.assign.to.specific.agent.maohao': 'Assign to specific agent: ',
  'allocation.more.rules': 'For more rules, please "View Details"',
  'allocation.more.rules.detail.work.order': 'Ticket type',
  'allocation.more.rules.detail.channel.name': 'Channel name',
  'allocation.more.rules.detail.customer.tag': 'Customer tag',
  'allocation.more.rules.detail.customer.country': 'Customer country',
  'allocation.more.rules.detail.customer.phone': 'Customer phone',
  'allocation.more.rules.detail.customer.email': 'Customer email',
  'allocation.more.rules.detail.customer.level': 'Customer level',
  'allocation.more.rules.detail.customer.language': 'Customer language',
  'allocation.more.rules.detail.work.order.when': 'When "',
  'allocation.more.rules.detail.equal': ' equal to ',
  'allocation.more.rules.detail.and': 'and',
  'allocation.more.rules.index.and': 'and',
  'allocation.add.rule': 'Add rule',
  'allocation.add.judgment.rules': 'Add condition rule',
  'allocation.personal.rules':
    'When you configure multiple conditions, these conditions are',
  'allocation.personal.relation': 'relationship',
  'allocation.personal.when': 'When',
  'allocation.personal.include': ' include ',
  'allocation.rules': 'Assignment rules',
  'allocation.default.rules': 'Default rules',
  'allocation.default.tooltip.rules':
    'When the system does not match any other rules, it will route to the default rule. Please set the default assignment object for the ticket.',
  'allocationRule.valid.item':
    'The personalized configuration of each rule must have at least one valid item',
  'allocation.more.rules.detail.chnannel.name': 'Channel name',
  'allocationRule.channel.rules.type': 'Determine type',
};
