export default {
  'statistics.work.report.title': 'Workload report for agent',
  'statistics.work.report.label.1': 'Agent group: ',
  'statistics.work.report.label.1.1': 'Agent group',
  'statistics.work.report.label.2': 'Ticket type: ',
  'statistics.work.report.label.3': 'Select time: ',
  'statistics.work.report.label.4': 'Filter channels',
  'statistics.work.report.label.3.placeholder': 'Please choose a date',
  'statistics.work.report.label.1.placeholder': 'Please select an agent group',
  'statistics.work.report.label.2.placeholder': 'Please select ticket type',
  'statistics.work.report.series.name.delay':
    'Number of overdue unresolved tickets',
  'statistics.work.report.series.name': 'Number of unresolved tickets',
  // 数据明细
  'statistics.data.details.title': 'Contact Details',
  'statistics.data.details.select.date': 'Select time: ',
  'statistics.data.details.search.tips':
    'Please enter contact ID, agent, agent group, or queue to search',
  'statistics.data.details.table.contact.id': 'Contact ID',
  'statistics.data.details.table.incoming.call.time': 'Start time',
  'statistics.data.details.table.incoming.call.time.start':
    'Agent response time',
  'statistics.data.details.table.end.time': 'ACW start time',
  'statistics.data.details.table.acw.time': 'ACW end time',
  'statistics.data.details.table.total.call.duration': 'Total duration',
  'statistics.data.details.table.interaction.time': 'Interactive duration',
  'statistics.data.details.table.queue.waiting.time': 'Queue duration',
  'statistics.data.details.table.incoming.call.channel': 'Channel',
  'statistics.data.details.table.reception.seat': 'Agent',
  'statistics.data.details.table.seating.group': 'Agent Group',
  'statistics.data.details.table.queue': 'Queue',
  'statistics.data.details.table.queue.in': 'Inbound / Outbound',
  'statistics.data.details.table.phone.customer': 'Customer phone',
  'statistics.data.details.table.acw.duration': 'ACW duration',
  'statistics.data.details.table.work.order.number': 'Ticket number',
  'statistics.data.details.table.on.hold.time': 'Agent longest hold duration',
  'statistics.data.details.table.on.hold.num': 'Number of holds',
  'statistics.data.details.table.is.switch': 'Transfer',
  'statistics.data.details.table.hanging.type': 'Disconnect reason',
  'statistics.data.details.table.system.phone': 'System phone',
  'statistics.data.details.table.contact.id.id': 'Initial contact ID',
  'statistics.data.details.table.contact.id.previous': 'Previous contact ID',
  'statistics.data.details.table.contact.id.next': 'Next contact ID',
  'statistics.data.details.table.satisfaction.rating': 'Satisfaction score',
  'statistics.data.details.table.operation': 'Operation',
  'statistics.data.details.channel.type': 'Channel type: ',
  // 座席历史工作指标
  'statistics.agent.performance.indicators.title': 'Agent Historical Metrics',
  'statistics.agent.performance.indicators.tips': 'Please enter the agent name',
  'statistics.agent.performance.indicators.table.agent.name': 'Agent name',
  'statistics.agent.performance.indicators.table.accumulated.online.duration':
    'Online time',
  'statistics.agent.performance.indicators.table.accumulated.idle.duration':
    'Agent idle time',
  'statistics.agent.performance.indicators.table.accumulated.reception.duration':
    'Agent on contact time',
  'statistics.agent.performance.indicators.table.task.time.utilization.rate':
    'Occupancy',
  'statistics.agent.performance.indicators.table.unresponsive.quantity':
    'Agent non-response',
  'statistics.agent.performance.indicators.table.reception.contacts.quantity':
    'Contacts handled',
  'statistics.agent.performance.indicators.table.response.rate':
    'Agent answer rate',
  'statistics.agent.performance.indicators.table.working.hours.after.contact':
    'Average after contact work time',
  'statistics.agent.performance.indicators.table.customer.retention.time':
    'Average customer hold time',
  // 历史队列指标
  'statistics.historical.queue.indicators.title': 'Queue History Metrics',
  'statistics.historical.queue.indicators.table.queue.name': 'Queue name',
  'statistics.historical.queue.indicators.table.queue.name.label':
    'Queue name: ',
  'statistics.historical.queue.indicators.search.tips':
    'Please enter the name of the queue',
  'statistics.historical.queue.indicators.table.working.hours.after.contact':
    'Average after contact work time',
  'statistics.historical.queue.indicators.table.agent.interaction.time':
    'Average agent interaction time',
  'statistics.historical.queue.indicators.table.customer.retention.time':
    'Average customer hold time',
  'statistics.historical.queue.indicators.table.queue.abandonment.time':
    'Average queue abandon time',
  'statistics.historical.queue.indicators.table.queue.waiting.time':
    'Average queue answer time',
  'statistics.historical.queue.indicators.table.abandon.contact.quantity':
    'Contacts abandoned',
  'statistics.historical.queue.indicators.table.queued.contacts.quantity':
    'Contacts queued',
  //队列维度
  'statistics.queue.title': 'Queue Real-Time Metrics',
  'statistics.queue.title.right': 'Update data time: ',
  'statistics.queue.title.1': 'Agent Status List',
  'statistics.queue.title.2': 'Contact List',
  'statistics.queue.title.3': 'Performance List',
  'statistics.queue.title.btn': 'Batch export',
  'statistics.queue.table.agent.1': 'Queue name',
  'statistics.queue.table.agent.2': 'Online',
  'statistics.queue.table.agent.3': 'On contact',
  'statistics.queue.table.agent.4': 'NPT',
  'statistics.queue.table.agent.5': 'ACW',
  'statistics.queue.table.agent.6': 'Error',
  'statistics.queue.table.agent.7': 'Available',
  'statistics.queue.table.contact.2':
    'Available (number of contactable contacts)',
  'statistics.queue.table.contact.3': 'Active (number of connected contacts)',
  'statistics.queue.table.modal.search':
    'Please enter the metrics name to search',
  'statistics.queue.table.modal.panel.1': 'Performance ({num})',
  'statistics.queue.table.modal.panel.2':
    'Contacts Abandoned in X ({num}) seconds',
  'statistics.queue.table.modal.panel.3':
    'Contacts Answered in X ({num}) seconds',
  'statistics.queue.table.modal.panel.4':
    'Contact Person Service Level ({num})',
  'statistics.queue.table.modal.panel.1.check.1': 'Oldest',
  'statistics.queue.table.modal.panel.1.check.2': 'Scheduled',
  'statistics.queue.table.modal.panel.1.check.3': 'Queued',
  'statistics.queue.table.modal.panel.1.check.4': 'Handled',
  'statistics.queue.table.modal.panel.1.check.5': 'Abandoned',
  'statistics.queue.table.modal.panel.1.check.6': 'AHT',
  'statistics.queue.table.modal.panel.1.check.7': 'Handled out',
  'statistics.queue.table.modal.panel.1.check.8': 'API contacts handled',
  'statistics.queue.table.modal.panel.1.check.9': 'Callback contacts handled',
  'statistics.queue.table.modal.panel.1.check.10': 'Transferred out from queue',
  'statistics.queue.table.modal.panel.1.check.11': 'Avg queue answer time',
  'statistics.queue.table.modal.panel.1.check.12': 'Avg abandon time',
  'statistics.queue.table.modal.panel.1.check.13': 'Average Active Time',
  'statistics.queue.table.modal.panel.1.check.14':
    'Avg interaction and hold time',
  'statistics.queue.table.modal.panel.1.check.15': 'Avg interaction time',
  'statistics.queue.table.modal.panel.1.check.16': 'Average resolution time',
  'statistics.queue.table.modal.panel.1.check.17': 'In queue',
  'statistics.queue.table.modal.panel.1.check.18': 'Transferred out',
  'statistics.queue.table.modal.panel.1.check.19': 'Max queued',
  'statistics.queue.table.modal.panel.1.check.20': 'Agent non-response',
  'statistics.queue.table.modal.panel.1.check.21': 'Transferred out by agent',
  'statistics.queue.table.modal.panel.1.check.22': 'Average Agent Pause Time',
  'statistics.queue.table.modal.panel.1.check.23':
    'Average API Connecting Time',
  'statistics.queue.table.modal.panel.1.check.24': 'Agent non-response',
  'statistics.queue.table.modal.panel.1.check.25':
    'Contacts Abandoned in {num} seconds',
  'statistics.queue.table.modal.panel.1.check.26':
    'Contacts Answered in {num} seconds',
  'statistics.queue.table.modal.panel.1.check.25.search':
    'Contacts Abandoned in',
  'statistics.queue.table.modal.panel.1.check.26.search':
    'Contacts Answered in',
  //座席维度
  'statistics.agent.title': 'Agent Real-Time Metrics',
  'statistics.agent.title.1': 'Agent List',
  'statistics.agent.table.modal.panel.1': 'Agent ({num})',
  'statistics.agent.table.agent.1': 'Agent Name',
  'statistics.agent.table.agent.2': 'Agent First Name',
  'statistics.agent.table.agent.3': 'Agent Last Name',
  'statistics.agent.table.agent.4': 'Capacity',
  'statistics.agent.table.agent.5': 'Agent login name',
  'statistics.agent.table.agent.5.label': 'Agent login name: ',
  'statistics.agent.table.agent.5.label.placeholder':
    'Please enter the agent login name',
  'statistics.agent.table.agent.6': 'Next activity',
  'statistics.agent.table.agent.7': 'Activity',
  'statistics.agent.table.agent.8': 'Duration',
  'statistics.agent.table.agent.9': 'Channel',
  'statistics.agent.table.contact.1': 'Queue',
  'statistics.agent.table.contact.2': 'Duration',
  'statistics.agent.table.contact.3':
    'Contact status (whether received or not)',
  'statistics.agent.table.contact.4': 'Availability',
  'statistics.agent.table.modal.panel.1.check.1': 'Handled out',
  'statistics.agent.table.modal.panel.1.check.2': 'API contacts handled',
  'statistics.agent.table.modal.panel.1.check.3': 'Callback contacts handled',
  'statistics.agent.table.modal.panel.1.check.4': 'Avg hold time',
  'statistics.agent.table.modal.panel.1.check.5': 'Average active time',
  'statistics.agent.table.modal.panel.1.check.6': 'Avg interaction time',
  'statistics.agent.table.modal.panel.1.check.7': 'Transferred out',
  'statistics.agent.table.modal.panel.1.check.8': 'Agent non-response',
  'statistics.agent.table.modal.panel.1.check.9': 'Average agent pause time',
  'statistics.agent.table.modal.panel.1.check.10': 'Avg API connecting time',
  'statistics.agent.table.modal.panel.1.check.11': 'Callback contacts handled',
  'statistics.agent.table.modal.panel.1.check.12': 'Transferred out',
  'statistics.agent.table.modal.panel.1.check.13':
    'Avg incoming connecting time',
  // 座席工作效率统计
  'agent.work.efficiency.statistics.title': 'Agent Work Efficiency Reports',
  'agent.work.efficiency.statistics.title.group':
    'Agent Group Work Efficiency Reports',
  'agent.work.efficiency.statistics.second.title.1': 'Average resolution time',
  'agent.work.efficiency.statistics.second.title.2': 'Data insight ',
  'agent.work.efficiency.statistics.data.insight.agent.group': 'Agent group ',
  'agent.work.efficiency.statistics.data.insight.agent': 'Agent ',
  'agent.work.efficiency.statistics.data.insight.agent.group.1':
    'The average efficiency of agent group ',
  'agent.work.efficiency.statistics.data.insight.agent.1':
    'The average efficiency of agent ',
  'agent.work.efficiency.statistics.data.insight.1':
    'During this period, the average time for processing tickets by agent ',
  'agent.work.efficiency.statistics.data.insight.1.group':
    'During this period, the average time for processing tickets by agent group ',
  'agent.work.efficiency.statistics.data.insight.2':
    ' is the longest, reaching an average of ',
  'agent.work.efficiency.statistics.data.insight.3':
    '. Please pay attention to the work situation. The average time for processing tickets by ',
  'agent.work.efficiency.statistics.data.insight.4':
    ' is the shortest, reaching an average of ',
  'agent.work.efficiency.statistics.data.insight.5':
    '. Please encourage them to keep up the good work. ',
  'agent.work.efficiency.statistics.data.insight.6':
    ' has increased the most, which has increased by ',
  'agent.work.efficiency.statistics.data.insight.7':
    '. Please encourage them to maintain this performance. ',
  'agent.work.efficiency.statistics.data.insight.8':
    ' has increased the smallest, which has dropped by ',
  'agent.work.efficiency.statistics.data.insight.9':
    ', please pay attention to the work situation.',
  'agent.work.efficiency.statistics.second.title.3':
    'Top 3 longest average resolution times ',
  'agent.work.efficiency.statistics.second.title.3.1':
    'Top 3 shortest average resolution times',
  'agent.work.efficiency.statistics.second.title.3.2':
    'Top 3 efficiency improvements',
  'agent.work.efficiency.statistics.second.title.3.3':
    'Top 3 efficiency declines',
  'agent.work.efficiency.statistics.agent.name': 'Agent name',
  'agent.work.efficiency.statistics.agent.name.group': 'Agent group ',
  'agent.work.efficiency.statistics.processing.time': 'Processing time ',
  'agent.work.efficiency.statistics.efficiency.improvement':
    'Efficiency improvement',
  'agent.work.efficiency.statistics.efficiency.decline': 'Efficiency decline',
  'agent.work.efficiency.statistics.second.title.4':
    'Average ticket resolution time',
  'agent.work.efficiency.statistics.agent.name.select': 'Agent name: ',
  'agent.work.efficiency.statistics.agent.name.select.placeholder':
    'Multiple agents can be selected for data comparison',
  'agent.work.efficiency.statistics.agent.name.select.placeholder.team':
    'You can select multiple agent groups for data comparison',
  'agent.work.efficiency.statistics.table.agent.name': 'Agent name',
  'agent.work.efficiency.statistics.second.title.5':
    'Trend of average ticket resolution time',
  'agent.work.efficiency.statistics.hour.text': 'hour',
  'agent.work.efficiency.statistics.day.text': 'day',
  'agent.work.efficiency.statistics.week.text': 'week',
  'agent.work.efficiency.statistics.month.text': 'month',
  'agent.work.efficiency.statistics.second.title.6':
    'Distribution of average ticket resolution times',
  'agent.work.efficiency.statistics.second.title.7': 'Ticket SLA report',
  'agent.work.efficiency.statistics.table.time': 'Time',
  // 满意度报表
  'satisfaction.report.title': 'Agent Satisfaction Reports',
  'satisfaction.report.title.group': 'Agent Group Satisfaction Reports',
  'satisfaction.report.table.rating': 'Satisfaction rating',
  'satisfaction.report.second.title.1': 'Average satisfaction score',
  'satisfaction.report.second.title.2': 'Reply satisfaction ratio',
  'satisfaction.report.second.title.3':
    'Top 3 highest average satisfaction scores',
  'satisfaction.report.second.title.4':
    'Top 3 lowest average satisfaction scores',
  'satisfaction.report.second.title.5': 'Top 3 satisfaction improvements',
  'satisfaction.report.second.title.6': 'Top 3 satisfaction declines',
  'satisfaction.report.second.title.7': 'Average satisfaction score',
  'satisfaction.report.second.title.8': 'Trend of average satisfaction scores',
  'satisfaction.report.second.title.9':
    'Distribution of satisfaction scores by ticket type',
  'satisfaction.report.second.title.10':
    'Distribution of satisfaction scores by channel type',
  'satisfaction.report.second.title.11': 'Reply satisfaction ratio',
  'satisfaction.report.data.insight.agent.group':
    'The satisfaction score of agent group ',
  'satisfaction.report.data.insight.agent': 'The satisfaction score of agent ',
  'satisfaction.report.data.insight.1':
    'During this period, the satisfaction score of agent ',
  'satisfaction.report.data.insight.1.group':
    'During this period, the satisfaction score of agent group ',
  'satisfaction.report.data.insight.2': ' was the highest, reaching ',
  'satisfaction.report.data.insight.3': ' points. ',
  'satisfaction.report.data.insight.4': ' was the lowest, reaching ',
  'satisfaction.report.data.insight.5': ' points. ',
  'satisfaction.report.data.insight.6': ' increased the most, increasing by ',
  'satisfaction.report.data.insight.7': ' points. ',
  'satisfaction.report.data.insight.8': ' dropped the most, down ',
  'satisfaction.report.data.insight.9': ' points.',
  'satisfaction.report.satisfaction.score': 'Avg satisfaction score',
  'satisfaction.report.improve.average.score': 'Increase average score',
  'satisfaction.report.decreased.average.score': 'Decrease in average score',
  'satisfaction.report.average.satisfaction.score': 'points',
  // 客户工单Top10
  'customer.ticket.title': 'Customer Ticket Reports',
  'customer.ticket.ranking': 'Ranking',
  'customer.ticket.customer.ticket.number': 'Number',
  'customer.ticket.customer.name': 'Customer name',
  // 新增座席组国际化
  'agent.work.efficiency.statistics.agent.name.select.placeholder.1':
    'Multiple agent groups can be selected for data comparison',
  // 工作效率统计气泡提示框内容
  'work.efficiency.statistics.average.resolution.time.content.text':
    'Calculate the average resolution time of all tickets within a specified time period, Average resolution time = SUM (Resolution Time - Creation Time) / Total Number of Tickets',
  'work.efficiency.statistics.data.insight.content.text':
    'Perform in-depth data analysis based on ticket resolution time, efficiency, and other key metrics to identify critical data such as longest, shortest resolution times, efficiency improvements, and declines',
  'work.efficiency.statistics.average.duration.agent.content1.text':
    'Identify the top three agents with the longest average resolution times within a specified time period',
  'work.efficiency.statistics.average.duration.agent.content2.text':
    'Identify the top three agents with the shortest average resolution times within a specified time period',
  'work.efficiency.statistics.average.duration.agent.content3.text':
    'Identify the top three agents with the most reduced average resolution times within a specified time period',
  'work.efficiency.statistics.average.duration.agent.content4.text':
    'Identify the top three agents with the most increased average resolution times within a specified time period',
  'work.efficiency.statistics.average.time.resolve.tickets.text':
    'Report on the average resolution time of tickets for each agent within a specified time period',
  'work.efficiency.statistics.trend.average.time.resolve.tickets.text':
    'Report on the trend of average resolution times for each agent over time within a specified time period',
  'work.efficiency.statistics.distribution.processing.time.tickets.text':
    'Count the number of tickets within different resolution time intervals (e.g., 0-1 hour, 1-2 hours) within a specified time period',
  'work.efficiency.statistics.ticket.SLA.report.content.text':
    'Report on SLA compliance within a specified time period',
  'work.efficiency.statistics.select.time.tips.text':
    'The selected time range must be less than 24 hours.',
  // 满意度报表气泡提示框内容
  'satisfaction.report.average.satisfaction.score.content.text':
    'Calculate the average satisfaction score of all tickets within a specified time period, Average satisfaction score = Sum of ticket satisfaction scores / Total number of tickets with satisfaction scoresCalculate the average satisfaction score of all tickets within a specified time period, average satisfaction score = Sum of ticket satisfaction scores / Total number of tickets with satisfaction scores',
  'satisfaction.report.average.satisfaction.score.content1.text':
    ' Calculate the overall ticket reply satisfaction ratio within a specified time period, reply satisfaction ratio = Number of tickets with satisfaction scores / Total number of tickets',
  'satisfaction.report.data.insight.content.text':
    'Perform in-depth data analysis based on customer satisfaction and reply satisfaction ratio to identify critical data such as highest, lowest satisfaction scores, improvements, and declines',
  'satisfaction.report.average.duration.agent.content1.text':
    'Report on the average satisfaction score for each agent within a specified time period, Average satisfaction score = Sum of ticket satisfaction scores / Total number of tickets with satisfaction scores',
  'satisfaction.report.average.time.resolve.tickets.text':
    'Report on the trend of average satisfaction scores for each agent over time within a specified time period',
  'satisfaction.report.distribution.processing.time.tickets.text':
    'Report on the average satisfaction score by ticket type for each agent within a specified time period',
  'satisfaction.report.channel.text':
    'Report on the average satisfaction score by channel for each agent within a specified time period',
  'satisfaction.report.statistical.review.rate.agent.text':
    'Calculate the reply satisfaction ratio for each agent within a specified time period, reply satisfaction ratio = Number of tickets with satisfaction scores / Total number of tickets',
  'satisfaction.report.highest.average.satisfaction.content.text':
    'Identify the top three agents with the highest average satisfaction scores within a specified time period',
  'satisfaction.report.shortest.average.satisfaction.content.text':
    'Identify the top three agents with the lowest average satisfaction scores within a specified time period',
  'satisfaction.report.average.efficiency.improvements.content.text':
    'Identify the top three agents with the most improved average satisfaction scores within a specified time period',
  'satisfaction.report.decreases.average.efficiency.content.text':
    'Identify the top three agents with the most decreased average satisfaction scores within a specified time period',
  'agent.work.efficiency.statistics.time.unit': ' (Unit: Hour)',
  'agent.work.efficiency.statistics.number.unit': ' (Unit: pieces)',
  'satisfaction.report.select.agent.tips': 'Choose a maximum of five agents!',
  'satisfaction.report.select.agent.group.tips':
    'Choose no more than five agent groups!',
  //  'statistics.data.details.channel.type': 'Channel type: '
  'statistics.data.details.contact.id': 'Contact ID: ',
};
