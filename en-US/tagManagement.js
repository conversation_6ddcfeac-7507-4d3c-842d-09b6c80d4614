export default {
  'tag.management.tab.standard': 'Standard tags',
  'tag.management.tab.private': 'Private tags',
  'tag.management.tab.settings': 'Settings',
  'tag.management.private.search.placeholder':
    'Please enter the tag you want to search for',
  'tag.management.private.batch.open': 'Bulk public release',
  'tag.management.private.table.tag.content': 'Tag',
  'tag.management.private.table.tag.type': 'Tag types',
  'tag.management.private.table.creator': 'Creator',
  'tag.management.private.table.create.time': 'Creation time',
  'tag.management.private.table.operation': 'Operation',
  'tag.management.private.table.operation.open.standard':
    'Public as standard tag',
  'tag.management.private.table.operation.delete': 'Delete',
  'tag.management.private.table.operation.delete.tips':
    'Should the tag be deleted?',
  'tag.management.private.settings.allow.customize.tag':
    'Allow agents to customize private tags',
  'tag.management.standard.create.tag': 'Create tags',
  'tag.management.standard.editor.tag': 'Modify tags',
  'tag.management.standard.modal.tag.name': '标签内容：',
  'tag.management.standard.modal.tag.name.placeholder': '请填写标签内容',
  'tag.management.standard.modal.tag.name.placeholder.1':
    '请填写标签内容并回车生成标签',
  'tag.management.standard.modal.tag.classification': 'Category: ',
  'tag.management.standard.modal.tag.classification.placeholder':
    'Please select tag categories',
  'tag.management.standard.modal.p.text':
    'Tags created here are all customer tags.',
  'tag.work.table.agent.tips':
    'Enter a tag, press Enter to generate a private tag',
  'tag.work.table.agent.select.tips': 'Please select tags',
  'tag.management.standard.create.tips':
    'After entering a tag, press Enter to confirm. Ensure at least one tag is created before clicking the Save button.',
  'tag.management.standard.create.note':
    'Note: Enter a tag, select a color, and press Enter. You can add multiple tags simultaneously.',
  // 标签分类
  'tag.classification.title': 'Tag category',
  'tag.classification.create.tag': 'Create tag category',
  'tag.classification.editor.tag': 'Modify tag category',
  'tag.classification.private.search.placeholder':
    'Please enter the tag category you want to search for',
  'tag.classification.table.name': 'Tag category name',
  'tag.classification.table.operation.delete.tips':
    'Should the tag category be deleted?',
  'tag.classification.modal.name': 'Category: ',
  'tag.classification.modal.name.placeholder':
    'Please enter the name of the tag category',
  'tag.classification.modal.required.tips':
    'The category name of tags can only be composed of English uppercase and lowercase letters or Chinese characters.',
  'tag.classification.modal.required.max':
    'Tag category name can have up to 40 characters',
  'tag.management.standard.modal.tag.application':
    'Scope of application for labels: ',
  'tag.management.standard.tag.application': 'Scope of application for labels',
  'tag.management.standard.modal.tag.application.placeholder':
    'Please select the applicable scope of the label.',
  'tag.management.standard.modal.customer.tag': 'Customer tags',
  'tag.management.standard.modal.knowledge.tag': 'Knowledge tags',
  'tag.management.standard.modal.work.order.label.tag': 'Ticket tags',
  'tag.management.tab.private.customer': 'Private customer tag',
  'tag.management.tab.private.ticket': 'Private ticket tag',
};
