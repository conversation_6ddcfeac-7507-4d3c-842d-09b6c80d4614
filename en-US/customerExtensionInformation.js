// export default {
//   'customer.ext.info': 'Customize customer extension information',
//   'customer.ext.info.new': 'Customize customer properties',
//   'customer.ext.info.set': 'Properties',
//   'customer.ext.info.add': 'additive attribute',
//   'customer.ext.info.name': 'Attribute name',
//   'customer.ext.info.code': 'Attribute code',
//   'customer.ext.info.type': 'Attribute type',
//   'customer.ext.info.require': 'Required or not',
//   'customer.ext.info.tipsInfo': 'Prompt message',
//   'customer.ext.info.label': 'Label',
//   'customer.ext.info.value': 'Value',
//   'customer.ext.info.multiple': 'Multiple choice or not',
//   'customer.ext.info.tips': 'Hint',
//   'customer.ext.info.operate': 'Operation',
//   'customer.ext.info.modal.title': 'Edit client properties',
//   'customer.worker.info.modal.title': 'Edit ticket properties',
//   'customer.ext.info.selectList': 'Tab control',
//   'customer.ext.info.save': 'Save',
//   'customer.ext.info.cancel': 'Cancel',
//   'customer.ext.info.code.not.empty':
//     'The encoding in line {lineNum} cannot be empty！',
//   'customer.worker.info.select.placeholder': 'Please select an attribute type',
//   'customer.ext.info.only.en':
//     'Only English alphabet can be entered for the code of line {lineNum}!',
//   'customer.ext.info.name.not.empty':
//     'The name of line {lineNum} cannot be empty!',
//   'customer.ext.info.name.input.limit':
//     'The name of line {lineNum} can only be entered in Chinese, English, and numbers!',
//   'customer.ext.info.repeat': 'Duplicate encoding in attribute: {repeatCode}',
//   'customer.ext.info.save.success': 'Save successful！',
//   'customer.ext.info.at.least.commit':
//     'Please add at least one non template row data before saving!',
// };
export default {
  'customer.ext.info': 'Custom customer extension information',
  'customer.ext.info.new': 'Custom customer attributes',
  'customer.ext.info.set': 'Attribute settings',
  'customer.ext.info.add': ' Add attribute',
  'customer.ext.info.name': 'Attribute name',
  'customer.ext.info.code': 'Attribute code',
  'customer.ext.info.type': 'Attribute type',
  'customer.ext.info.require': 'Is required',
  'customer.ext.info.multiple': 'Multi-selection',
  'customer.ext.info.label': 'Display name',
  'customer.ext.info.value': 'Option value',
  'customer.ext.info.tipsInfo': 'Prompt message',
  'customer.ext.info.tips': 'Prompt',
  'customer.ext.info.operate': 'Action',
  'customer.ext.info.selectList': 'Tab',
  'customer.ext.info.modal.title': 'Edit customer attributes',
  'customer.worker.info.modal.title': 'Edit ticket attributes',
  'customer.worker.info.modal.title.add': 'Add ticket attributes',
  'customer.worker.info.select.placeholder': 'Select attribute type',
  // 'customer.ext.info.name': 'Name',
  'customer.ext.info.save': 'Save',
  'customer.ext.info.selectList.work': 'Add type',
  'customer.ext.info.cancel': 'Cancel',
  'customer.ext.info.code.not.empty': 'Code in line {lineNum} cannot be empty!',
  'customer.ext.info.only.en':
    'The code in line {lineNum} can only contain English letters!',
  'customer.ext.info.name.not.empty':
    'The name in line {lineNum} cannot be empty!',
  'customer.ext.info.name.input.limit':
    'The name in line {lineNum} can only contain Chinese, English, and numbers!',
  'customer.ext.info.repeat':
    'Duplicate code {repeatCode} exists in the properties',
  'customer.ext.info.save.success': 'Save successful!',
  'customer.ext.info.at.least.commit':
    'Please add at least one non-template line of data before saving!',
  'customer.ext.popconfirm.delete': 'Confirm deletion?',
  'new.customerDataGroupManagement.btn.1': 'Batch remove',
  'new.customerDataGroupManagement.btn.2': 'Batch upload',
  'new.customerDataGroupManagement.btn.3': 'Add customer',
};
