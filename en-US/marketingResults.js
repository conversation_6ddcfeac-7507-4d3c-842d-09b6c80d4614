export default {
  // 营销结果
  'marketing.results.activity.name.placeholder':
    'Please select an campaign name',
  'marketing.results.marketing.channel.type': 'Channel Type:',
  'marketing.results.marketing.channel.type.placeholder':
    'Please select the marketing channel type',
  'marketing.results.marketing.event.name': 'Event Name:',
  'marketing.results.marketing.event.name.placeholder':
    'Please select the name of the marketing event',
  'marketing.results.table.activity.name': 'Campaign name',
  'marketing.results.table.marketing.channel.type': 'Channel type',
  'marketing.results.table.send.channel': 'Delivery channel',
  'marketing.results.table.marketing.events': 'Event name',
  'marketing.results.table.marketing.methods': 'Marketing method',
  'marketing.results.table.test.type': 'Types of A/B Test',
  'marketing.results.table.marketing.event.batches': 'Event Batch',
  'marketing.results.table.number.customers': 'Number of customers',
  'marketing.results.table.delivery.rate': 'Delivery rate',
  'marketing.results.table.read.rate': 'Open rate',
  'marketing.results.table.click.rate': 'Click rate',
  'marketing.results.table.subscription.rate': 'Subscription rate',
  'marketing.results.table.unsubscribe.rate': 'Unsubscribe rate',
  'marketing.results.table.complaint.rate': 'Complaint rate',
  'marketing.results.table.complaint.rate1': 'Complaint rate',
  'marketing.results.table.failure.rate': 'Failure rate',
  'marketing.results.table.bounce.rate': 'Bounce rate',
  'marketing.results.table.delivery.delay.rate': 'Delivery delay rate',
  'marketing.results.table.reject.rate': 'Reject rate',
  'marketing.results.table.rendering.failure.rate': 'Rendering failure rate',
  'marketing.results.table.operation': 'Operation',
  'marketing.results.table.export.customer': 'Export customer list',
  'marketing.results.table.customer.list': 'Customer List',
  'marketing.results.table.marketing.event.sending.time':
    'Marketing event send time',
  // 营销详情
  'marketing.results.marketing.event.batches': 'Event Batch:',
  'marketing.results.marketing.event.batches.placeholder':
    'Please select the batch of marketing events',
  'marketing.results.marketing.methods': `Marketing method: `,
  'marketing.results.marketing.methods.1': 'Standard test',
  'marketing.results.marketing.methods.2': 'A/B Test',
  'marketing.results.marketing.methods.3': 'Plan A',
  'marketing.results.marketing.methods.4': 'Plan B',
  'marketing.results.marketing.methods.placeholder': 'Please choose a method',
  'marketing.results.contact.information': 'Contact Information:',
  'marketing.results.contact.information.placeholder':
    'Please enter contact information',
  'marketing.results.table.customer.name': 'Customer Name',
  'marketing.results.table.customer.contact.information':
    'Customer contact information',
  'marketing.results.table.status': 'Status',
  'marketing.results.table.failure.reason': 'Reason for failure',
  'marketing.results.table.detail': 'Details',
  'event.notification.status.service': 'Delivery',
  'event.notification.status.read': 'Open',
  'event.notification.status.click': 'Click',
  'event.notification.status.subscribe': 'Subscription',
  'event.notification.status.unsubscribe': 'Unsubscribe',
  'event.notification.status.fail': 'Rendering Failure',
  'event.notification.status.complaint': 'Complaint',
  'event.notification.status.have.send': 'Sent',
  'event.notification.status.bounce': 'Bounce',
  'event.notification.status.reject': 'Reject',
  'event.notification.status.delivery.delay': 'DeliveryDelay',
  'marketing.details.customer.information.title': 'Customer Basic Information',
  'marketing.details.activity.information.title': 'Basic Campaign Information',
  'marketing.details.customer.information.customer.name': 'Customer name: ',
  'marketing.details.customer.information.customer.phone':
    'Customer phone number: ',
  'marketing.details.customer.information.customer.whats.app':
    'WhatsApp number: ',
  'marketing.details.customer.information.customer.email': 'Customer email: ',
  'marketing.details.customer.information.marketing.result':
    'Marketing results: ',
  'marketing.details.history.title': 'Historical records',
  'marketing.channel.type.email': 'Email',
  'marketing.channel.type.all': 'All channels',
  'marketing.channel.type.all.small': 'All channels',
  'marketing.channel.type.phone': 'Phone',
  'marketing.channel.type.whats.app': 'WhatsApp',
  'marketing.channel.type.info': 'SMS',
  'marketing.channel.type.chat': 'Web Chat',
  'marketing.channel.type.app.chat': 'App Chat',
  'marketing.channel.type.web.video': 'Web Live Video',
  'marketing.channel.type.app.video': 'App Live Video',
  'marketing.channel.type.amazon.message': 'Amazon Message',
  'marketing.channel.type.facebook': 'Facebook Messenger',
  'marketing.channel.type.instagram': 'Instagram',
  'marketing.channel.type.line': 'Line',
  'marketing.channel.type.weCom': 'WeChat Customer Service',
  'marketing.channel.type.weChat.official.account': 'WeChat Offcial Account',
  'marketing.channel.type.web.online.video': 'WEB Online Voice',
  'marketing.channel.type.app.online.video': 'APP Online Voice',
  'marketing.channel.type.twitter': 'Twitter',
  'marketing.channel.type.telegram': 'Telegram',
  'marketing.channel.type.weChat.mini.program': 'WeChat Mini Program',
  'marketing.channel.type.shopify': 'Shopify',
  'marketing.channel.type.google.play': 'Google Play',
  'marketing.channel.type.discord': 'Discord',
  // A/B测试分析页面
  'test.analysis.result.title': 'Analysis Results',
  'test.analysis.result.comparison.dimension': 'Comparative dimension:',
  'test.analysis.result.comparison.dimension.1': 'Target Audience',
  'test.analysis.result.comparison.dimension.2': 'Marketing Content',
  'test.analysis.result.comparison.dimension.3': 'Marketing time',
  'test.analysis.result.overall.result': 'Overall result:',
  'marketing.results.table.delivery.rate.1': 'Delivery rate:',
  'marketing.results.table.read.rate.1': 'Open rate:',
  'marketing.results.table.click.rate.1': 'Click rate:',
  'marketing.results.table.subscription.rate.1': 'Subscription rate:',
  'marketing.results.table.unsubscribe.rate.1': 'Unsubscribe rate:',
  'marketing.results.table.complaint.rate.1': 'Complaint rate:',
  'marketing.results.table.failure.rate.1': 'Failure rate:',
  'marketing.results.table.bounce.rate.1': 'Bounce rate:',
  'marketing.results.table.delivery.delay.rate.1': 'Delivery delay rate:',
  'marketing.results.table.reject.rate.1': 'Reject rate:',
  'marketing.results.table.rendering.failure.rate.1': 'Rendering failure rate:',
  'test.analysis.semicolon': ';',
  'test.analysis.period': '.',
  'marketing.channel.type.discord': 'Discord',
};
