export default {
  // Navigation Menu
  'header.home': 'Homepage',
  'header.product': 'Products',
  'header.solution': 'Solutions',
  'header.partner': 'Partners',
  'header.resource': 'Resources',
  'header.company.news': 'Company News',
  // Product Dropdown
  'header.product.channel': 'Omni-channel Access',
  'header.product.channel.desc':
    'One-stop customer service for mainstream channels like WhatsApp, Facebook, Instagram, and WeChat',
  'header.product.ai.call.center': 'AI Call Center',
  'header.product.ai.call.center.desc':
    'Ready to use, stable calling service with lines and numbers in 80+ countries globally',
  'header.product.ai.agent': 'AI Agent',
  'header.product.ai.agent.desc':
    'AI Agent quickly answers customer questions, intelligently guides interest, and helps improve customer experience and business efficiency',
  'header.product.aigc': 'AIGC Smart Service',
  'header.product.aigc.desc':
    'Create your exclusive knowledge base, combined with natural language understanding, for 24/7 automated and precise customer inquiry responses',
  'header.product.aigc.assistant': 'AIGC Agent Assistant',
  'header.product.aigc.assistant.desc':
    'Multilingual translation, conversation summary, customer emotion recognition to quickly improve work efficiency and employee satisfaction',
  'header.product.smart.work.order': 'Smart Work Order',
  'header.product.smart.work.order.desc':
    'Priority setting, intelligent dispatch, work order classification',
  'header.product.ai.voice.robot': 'AI Voice Robot',
  'header.product.ai.voice.robot.desc':
    'Intent understanding, multi-turn dialogue for dining, booking, ticketing, and marketing scenarios',
  'header.product.aigc.marketing': 'AIGC Marketing',
  'header.product.aigc.marketing.desc':
    'Marketing creative and content auto-generation, automated workflow',
  'header.product.video.service': 'Video Customer Service',
  'header.product.video.service.desc':
    'Real-time video calls for more personal service',
  'header.product.data.report': 'Data Reports',
  'header.product.data.report.desc':
    'Multi-dimensional data analysis to identify potential issues and business growth opportunities',
  // Solution Dropdown
  'header.solution.finance': 'Financial Industry',
  'header.solution.retail': 'Retail Industry',
  'header.solution.manufacturing': 'Manufacturing Industry',
  'header.solution.consumer.electronics': 'Consumer Electronics',
  'header.solution.new.energy': 'New Energy Industry',
  // Resource Dropdown
  'header.resource.help.center': 'Help Center',
  'header.resource.blog': 'Blog',
  // Right Menu
  'header.contact.us': 'Contact Us',
  'header.demo': 'Demo',
  'header.login': 'Login',
  'header.register': 'Sign up',
};
