export default {
  'intelligent.form.filling.title': 'Smart form filling',
  'intelligent.form.filling.btn.add': 'Smart form filling configuration',
  'intelligent.form.filling.table.language': 'Language',
  'intelligent.form.filling.table.attribute.name': 'Attribute name',
  'intelligent.form.filling.table.create.time': 'Creation time',
  'intelligent.form.filling.table.modify.time': 'Modified time',
  'contact.customers.title.configure.properties': 'Configure attributes',
  'contact.customers.form.attribute.name': 'Attribute name:',
  'contact.customers.form.attribute.name.required': 'Enter attribute name',
  'contact.customers.form.attribute.example.values': 'Attribute example value:',
  'contact.customers.form.attribute.example.values.required':
    'Enter attribute example value',
  'contact.customers.form.attribute.example.values.placeholder':
    'Enter an example value (e.g., an order number). This helps AIGC extraction.',
  'contact.customers.form.attribute.description': 'Property Format:',
  'contact.customers.form.attribute.description.required':
    'Enter property Format',
  'contact.customers.form.attribute.description.placeholder':
    'Describe the attribute\'s format characteristics (e.g., "Order number is 16 digits"). This helps AIGC extraction.',
  'contact.customers.form.save.attribute': 'Store attribute',
  'contact.customers.form.store.attribute': 'Store attribute to:',
  'contact.customers.form.store.attribute.required':
    'Select destination: Ticket or Customer profile',
  'contact.customers.form.ticket': 'Ticket',
  'contact.customers.form.customer': 'Customer profile',
  'contact.customers.form.select.attribute': 'Select attribute:',
  'contact.customers.form.select.attribute.required': 'Select attribute',
  'contact.customers.form.add.attribute': 'Add attribute',
  'contact.customers.title.trigger.intelligent.agent': 'Trigger bot',
  'intelligent.form.filling.form.select.intelligent.agent': 'Select bot:',
  'intelligent.form.filling.form.select.intelligent.agent.placeholder':
    'Select bot',
  'intelligent.form.filling.worktable.customer.name': 'Customer name:',
  'intelligent.form.filling.worktable.customer.name.required':
    'Enter customer name',
  'intelligent.form.filling.worktable.phone.number': 'Mobile number:',
  'intelligent.form.filling.worktable.phone.number.required':
    'Enter mobile number',
  'intelligent.form.filling.worktable.order.id': 'Order ID',
  'intelligent.form.filling.worktable.order.id.required': 'Enter Order ID',
  'intelligent.form.filling.worktable.effective.content.tips':
    'No valid content for smart form filling',
  'intelligent.form.filling.ticket.type.default': 'Default',
};
