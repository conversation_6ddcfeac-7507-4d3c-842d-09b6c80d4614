export default {
  'agentWorkloadReport.title': 'Agent Workload Report',
  'agentWorkloadReport.title.admin': 'Agent Group Workload Reports',
  'agentWorkloadReport.title.right': 'Select time:    ',
  'agentWorkloadReport.card.1.title': 'Total number of tickets',
  'agentWorkloadReport.card.title.tips.1':
    'Count the total number of tickets within a specified time period',
  'agentWorkloadReport.card.title.tips.2':
    'Count the number of unresolved tickets (including statuses: In Progress, Unassigned) within a specified time period',
  'agentWorkloadReport.card.title.tips.3':
    'Perform in-depth analysis on ticket data to uncover valuable information and trends, including highest, lowest, increasing, and decreasing ticket volumes',
  'agentWorkloadReport.card.title.tips.4':
    'Identify the top three agents with the highest number of tickets within a specified time period',
  'agentWorkloadReport.card.title.tips.5':
    'Identify the top three agents with the lowest number of tickets within a specified time period',
  'agentWorkloadReport.card.title.tips.6':
    'Identify the top three agents with the most increased tickets within a specified time period',
  'agentWorkloadReport.card.title.tips.7':
    'Identify the top three agents with the most decreased tickets within a specified time period',
  'agentWorkloadReport.card.title.tips.8':
    'Count the total number of tickets handled by each agent, including In Progress, Resolved, Terminated, and Transferred tickets, within a specified time period',
  'agentWorkloadReport.card.title.tips.9':
    'Display the trend of ticket volumes for different agents over time, and show the trend changes for five agents',
  'agentWorkloadReport.card.title.tips.10':
    'Count the number of tickets by channel type for each agent within a specified time period',
  'agentWorkloadReport.card.title.tips.11':
    'Count the number of tickets by status within a specified time period',
  'agentWorkloadReport.card.title.tips.12':
    'Count the number of tickets by type within a specified time period',
  'agentWorkloadReport.card.title.tips.13':
    'Count the number of tickets by priority within a specified time period',
  'agentWorkloadReport.card.1.tips': 'Compared to the previous ',
  'agentWorkloadReport.card.2.tips': ' days',
  'agentWorkloadReport.card.2.title': 'Total number of unresolved tickets',
  'agentWorkloadReport.card.3.title': 'Data insights',
  'agentWorkloadReport.card.4.title': 'Top 3 highest ticket volumes',
  'agentWorkloadReport.card.5.title': 'Top 3 lowest ticket volumes',
  'agentWorkloadReport.card.6.title': 'Top 3 ticket volume increases',
  'agentWorkloadReport.card.7.title': 'Top 3 ticket volume decreases',
  'agentWorkloadReport.card.table.1': 'Seat name',
  'agentWorkloadReport.card.table.2': 'Processed quantity',
  'agentWorkloadReport.card.table.3': 'Increase in quantity',
  'agentWorkloadReport.card.table.4': 'Decrease in quantity',
  'agentWorkloadReport.card.8.title':
    'Total number of tickets handled by the agent',
  'agentWorkloadReport.card.8.title.group':
    'Total number of tickets handled by the agent group',
  'agentWorkloadReport.card.9.title': 'Ticket volume trend',
  'agentWorkloadReport.card.10.title': 'Distribution of ticket by channel type',
  'agentWorkloadReport.card.11.title': 'Distribution of ticket statuses',
  'agentWorkloadReport.card.12.title': 'Distribution of ticket types',
  'agentWorkloadReport.card.13.title': 'Distribution of ticket priorities',
  'agentWorkloadReport.card.select.1': 'Seat name:',
  'agentWorkloadReport.card.select.1.placeholder':
    'Compare data across multiple seats',
  'agentWorkloadReport.table.1.title': 'Total number of tickets',
  'agentWorkloadReport.table.2.title': 'Time',
  'agent.work.report.data.insight':
    'During this period, agent {highestName} handled the highest number of tickets, with a total of {highestNumber} tickets processed. Please encourage them to keep up the good work. Agent {minimumName} handled the lowest number of tickets, with only {minimumNumber} tickets processed. Please pay attention to their work situation. ',
  'agent.work.report.data.insight.shangsheng':
    'Agent {riseHighestName} had the highest increase in ticket numbers, with an increase of {riseHighestNumber} tickets. Please encourage them to maintain this performance. ',
  'agent.work.report.data.insight.xiajiang':
    'Agent {declineHighestName} had the highest decrease in ticket numbers, with a decrease of {declineHighestNumber} tickets. Please pay attention to their work situation.',
  'agent.work.report.data.insight.admin':
    'During this period, agent group {highestName} handled the highest number of tickets, with a total of {highestNumber} tickets processed. Please encourage them to keep up the good work. Agent group {minimumName} handled the lowest number of tickets, with only {minimumNumber} tickets processed. Please pay attention to their work situation. ',
  'agent.work.report.data.insight.admin.1':
    'Agent group {riseHighestName} had the highest increase in ticket numbers, with an increase of {riseHighestNumber} tickets. Please encourage them to maintain this performance. ',
  'agent.work.report.data.insight.admin.2':
    'Agent group {declineHighestName} had the highest decrease in ticket numbers, with a decrease of {declineHighestNumber} tickets. Please pay attention to their work situation.',
  'agent.work.report.echarts.legend.1': 'Resolved tickets',
  'agent.work.report.echarts.legend.2': 'Unresolved tickets',
  'agent.work.report.echarts.legend.3':
    'Number of tickets waiting for customer response',
  'agent.work.machine.report.data.insight':
    'During this period, intelligent robots helped you handle {robotWorkTotalNumber} tickets, which is {robotContrastArtificialNumber} {trend1} than agent tickets; compared to the previous {daysBetween} days, it {trend2} by {robotContrastTimeNumber} tickets. Keep up the good work.',
  'trend.1': 'more',
  'trend.2': 'fewer',
  'trend.3': 'increased',
  'trend.4': 'decrease',
  'machineWorkloadReport.title': 'Chatbot Ticket Reports',
  'machineWorkloadReport.card.1.title': 'Total number of chatbot tickets',
  'machineWorkloadReport.card.2.title': 'Data insights',
  'machineWorkloadReport.card.3.title':
    'Total number of chatbot tickets by channel type',
  'machineWorkloadReport.card.4.title':
    'Distribution of chatbot ticket by channel type',
  'machineWorkloadReport.card.5.title': 'Chatbot vs. Agent',
  'machineWorkloadReport.card.6.title': 'Proportion of chatbot tickets',
  'machineWorkloadReport.table.date': 'Time',
  'machineWorkloadReport.table.WEB': 'Web chat',
  'machineWorkloadReport.table.WhatsApp': 'WhatsApp',
  'machineWorkloadReport.table.APP': 'APP chat',
  'machineWorkloadReport.table.machine.work.order.num': 'Chatbot tickets',
  'machineWorkloadReport.table.artificial.work.order.num': 'Agent tickets',
  'machineWorkloadReport.channel.web': 'Web chat',
  'machineWorkloadReport.channel.app': 'App chat',
  'machineWorkloadReport.channel.phone': 'Incoming call',
  'machineWorkloadReport.card.1.tips': 'Compared to the previous 15 days',
  'machineWorkloadReport.card.1.title.tips':
    'Count the total number of tickets handled by robots within a specified time period',
  'machineWorkloadReport.card.2.title.tips':
    'Perform in-depth analysis based on chatbot-handled ticket data to obtain comparison data between robots and humans',
  'machineWorkloadReport.card.3.title.tips':
    'Count the total number of tickets handled by robots in different channel types within a specified time period and report on the rise and fall trends from the previous period',
  'machineWorkloadReport.card.4.title.tips':
    'Report on the changes in the number of tickets handled by robots in different channel types within a specified time period',
  'machineWorkloadReport.card.5.title.tips':
    'Compare the number of tickets handled by robots with those handled by humans within a specified time period',
  'machineWorkloadReport.card.6.title.tips':
    'Calculate the proportion of tickets handled by robots within a specified time period',
};
