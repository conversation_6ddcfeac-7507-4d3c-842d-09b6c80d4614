export default {
  'aiAgentLibrary.banner.title': 'From Question to Action',
  'aiAgentLibrary.banner.subtitle': 'ConnectNow AI Agent',
  'aiAgentLibrary.tab.list.1': 'All',
  'aiAgentLibrary.tab.list.2': 'Financial',
  'aiAgentLibrary.tab.list.3': 'New Energy',
  'aiAgentLibrary.tab.list.4': 'Gaming',
  'aiAgentLibrary.tab.list.5': 'E-commerce',
  'aiAgentLibrary.tab.list.6': 'Manufacturing',
  'aiAgentLibrary.agent.list.content.1.title': 'Customer Pain Points',
  'aiAgentLibrary.agent.list.content.2.title': 'AI Agent Solution',
  'aiAgentLibrary.agent.list.content.3.title': 'Sample Dialogues',
  'aiAgentLibrary.agent.list.content.3.trigger.title': 'Trigger Method',
  'aiAgentLibrary.agent.list.1.title': 'Personalized Financial Product Advisor',
  'aiAgentLibrary.agent.list.1.industry': 'Finance',
  'aiAgentLibrary.agent.list.1.content.1.list.1':
    'Clients struggle to select appropriate financial products from numerous options that align with their financial situation, risk tolerance, and investment goals.',
  'aiAgentLibrary.agent.list.1.content.1.list.2':
    "Traditional risk assessment questionnaires are rigid and fail to deeply understand clients' genuine needs and potential concerns.",
  'aiAgentLibrary.agent.list.1.content.1.list.3':
    'High service thresholds for financial advisors prevent average clients from receiving personalized, timely investment advice.',
  'aiAgentLibrary.agent.list.1.content.2.list.1':
    'This intelligent agent analyzes client investment capital and risk preferences to deliver personalized, natural language recommendations for financial products.',
  'aiAgentLibrary.agent.list.1.content.3.list.1':
    "I want to invest but don't know what to choose. I have $100,000 in disposable funds.",
  'aiAgentLibrary.agent.list.1.content.3.list.2':
    "I'm interested in wealth management. Can you recommend something for me?",
  'aiAgentLibrary.agent.list.2.title': 'Loan Application Assistant',
  'aiAgentLibrary.agent.list.2.industry': 'Finance',
  'aiAgentLibrary.agent.list.2.content.1.list.1':
    'Clients lack clarity on loan eligibility criteria and required documentation, resulting in time-consuming repeated inquiries.',
  'aiAgentLibrary.agent.list.2.content.1.list.2':
    'Bank loan officers spend excessive time on preliminary screening, document collection, and verification processes.',
  'aiAgentLibrary.agent.list.2.content.1.list.3':
    'Extended loan application procedures with limited transparency lead to poor customer experience.',
  'aiAgentLibrary.agent.list.2.content.2.list.1':
    'This solution effectively reduces manual intervention, accelerates preliminary loan processing, while maintaining highly personalized service experiences.',
  'aiAgentLibrary.agent.list.2.content.3.list.1':
    "I'd like to apply for a home renovation loan",
  'aiAgentLibrary.agent.list.2.content.3.list.2':
    "I'm interested in applying for a loan",
  'aiAgentLibrary.agent.list.3.title': 'Insurance Claims Express',
  'aiAgentLibrary.agent.list.3.industry': 'Finance',
  'aiAgentLibrary.agent.list.3.content.1.list.1':
    'Complex insurance claims processes leave clients uncertain about procedures following illness or accidents.',
  'aiAgentLibrary.agent.list.3.content.1.list.2':
    'Cumbersome documentation requirements make it difficult for clients to understand claim eligibility criteria within policy terms.',
  'aiAgentLibrary.agent.list.3.content.1.list.3':
    'Lack of transparency in the claims process prevents clients from receiving timely updates on status and progress.',
  'aiAgentLibrary.agent.list.3.content.2.list.1':
    'This solution significantly reduces claim inquiry time, enhances customer satisfaction, and alleviates customer service workload, delivering simultaneous improvements in insurance service efficiency and customer experience.',
  'aiAgentLibrary.agent.list.3.content.3.list.1':
    'I need to file a claim for surgery I had last week. How do I proceed?',
  'aiAgentLibrary.agent.list.3.content.3.list.2':
    'I want to submit an insurance claim',
  'aiAgentLibrary.agent.list.4.title': 'Insurance Knowledge Assistant',
  'aiAgentLibrary.agent.list.4.content.1.list.1':
    'Financial knowledge is complex and professional, making it difficult for average users to understand and apply',
  'aiAgentLibrary.agent.list.4.content.1.list.2':
    'Traditional financial knowledge sources are fragmented, making it challenging to find targeted answers',
  'aiAgentLibrary.agent.list.4.content.1.list.3':
    'Financial terminology is obscure and difficult to comprehend',
  'aiAgentLibrary.agent.list.4.content.1.list.4':
    'Financial policies update frequently, making it hard for users to stay current',
  'aiAgentLibrary.agent.list.4.content.1.list.5':
    'Lack of scenario-based financial knowledge interpretation, creating a gap between theory and practice',
  'aiAgentLibrary.agent.list.4.content.2.list.1':
    "This intelligent agent leverages a comprehensive financial knowledge base combined with LLM's natural language understanding capabilities to provide clear, scenario-based financial knowledge explanations, helping users better understand and apply financial concepts.",
  'aiAgentLibrary.agent.list.4.content.3.list.1':
    'What is insurable interest? For whom can a policyholder have insurable interest?',
  'aiAgentLibrary.agent.list.4.content.3.list.2':
    'Why do insurance applications require extensive health questionnaires, including family medical history?',
  'aiAgentLibrary.agent.list.5.title': 'Smart Lead Generation Assistant',
  'aiAgentLibrary.agent.list.5.industry': 'Finance',
  'aiAgentLibrary.agent.list.5.content.1.list.1':
    'Traditional form-based lead collection is rigid with low customer engagement',
  'aiAgentLibrary.agent.list.5.content.1.list.2':
    'Poor conversion rate from inquiry to lead capture',
  'aiAgentLibrary.agent.list.5.content.1.list.3':
    'Inconsistent lead information quality affects follow-up effectiveness',
  'aiAgentLibrary.agent.list.5.content.1.list.4':
    'Lack of personalized guidance and interaction',
  'aiAgentLibrary.agent.list.5.content.1.list.5':
    'Inability to identify high-value leads in real-time',
  'aiAgentLibrary.agent.list.5.content.2.list.1':
    'This intelligent agent collects customer information through natural dialogue, combining scenario-based interaction and value demonstration to improve lead conversion rates. It also structures collected information to support precise marketing initiatives.',
  'aiAgentLibrary.agent.list.5.content.3.list.1':
    "How much does your critical illness insurance cost? I'd like to know more.",
  'aiAgentLibrary.agent.list.5.content.3.list.2':
    "What's the premium for your life insurance?",
  // 游戏start
  'aiAgentLibrary.agent.list.game.1.title': 'Game Recharge Assistant',
  'aiAgentLibrary.agent.list.game.1.content.1.list.1':
    'Payment issues are the most common type of customer service inquiries in gaming',
  'aiAgentLibrary.agent.list.game.1.content.1.list.2':
    'Analyzing failed payments is complex and requires multiple troubleshooting steps',
  'aiAgentLibrary.agent.list.game.1.content.1.list.3':
    'Traditional customer service struggles to quickly analyze transaction records and provide precise solutions',
  'aiAgentLibrary.agent.list.game.1.content.2.list.1':
    'This solution intelligently identifies gaming payment issues, automatically queries transaction records, and provides personalized solutions, significantly reducing customer wait times and service staff pressure. The system handles common problems such as payments not being credited, payment anomalies, and provides real-time guidance and status updates, greatly enhancing the payment service experience and issue resolution efficiency.',
  'aiAgentLibrary.agent.list.game.1.content.3.list.1':
    "I just paid 100 dollars, but didn't receive any diamonds in the game. What's happening?",
  'aiAgentLibrary.agent.list.game.1.content.3.list.2':
    "I just made a payment, but it hasn't been credited to my account",
  'aiAgentLibrary.agent.list.game.2.title': 'Game Bug Report Assistant',
  'aiAgentLibrary.agent.list.game.2.content.1.list.1':
    'Players provide unclear descriptions when reporting bugs',
  'aiAgentLibrary.agent.list.game.2.content.1.list.2':
    'Customer service needs to repeatedly ask for basic information',
  'aiAgentLibrary.agent.list.game.2.content.1.list.3':
    'Lack of unified bug classification and priority system',
  'aiAgentLibrary.agent.list.game.2.content.1.list.4':
    'Players are unaware of bug processing status',
  'aiAgentLibrary.agent.list.game.2.content.2.list.1':
    'This solution quickly collects key bug information through simplified guided dialogue, enabling efficient problem classification and processing. The system automatically assigns priorities based on bug type and provides players with clear expectations of subsequent processes, significantly improving user experience and customer service efficiency.',
  'aiAgentLibrary.agent.list.game.2.content.3.list.1':
    "There's a system bug - my equipment strengthening failed, materials and gold were deducted, but the equipment didn't strengthen.",
  'aiAgentLibrary.agent.list.game.2.content.3.list.2':
    'The game is extremely laggy, completely unplayable.',
  'aiAgentLibrary.agent.list.game.3.title': 'Game Consultation Expert',
  'aiAgentLibrary.agent.list.game.3.content.1.list.1':
    'Numerous game events and frequent version updates create high demand for information about event rules, rewards, participation requirements, and update content. Announcements are scattered, making it difficult to quickly find answers.',
  'aiAgentLibrary.agent.list.game.3.content.2.list.1':
    'This solution intelligently identifies inquiries about game events and version updates, automatically providing event rules, reward acquisition methods, and update information, significantly reducing the time players spend searching for information.',
  'aiAgentLibrary.agent.list.game.3.content.3.list.1':
    'Are there any new events recently?',
  'aiAgentLibrary.agent.list.game.3.content.3.list.2':
    'What is the "Move! Money-Picking Immortal" gameplay in Dream Star Park?',
  // 游戏end
  // 新能源 start
  'aiAgentLibrary.agent.list.newEnergy.1.title': 'Fault Alert Consultation',
  'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.1':
    'Users need to quickly understand the causes and solutions when encountering power station faults',
  'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.2':
    'Reduce workload of customer service repeatedly answering the same fault questions',
  'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.3':
    'Provide standardized fault handling procedures to reduce risk of operational errors',
  'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.4':
    '24/7 real-time response to fault consultation needs',
  'aiAgentLibrary.agent.list.newEnergy.1.content.2.list.1':
    'This solution intelligently identifies faults and provides standardized handling procedures, significantly improving fault response efficiency, reducing manual customer service workload, and achieving dual improvements in maintenance efficiency and service experience. The 24/7 real-time response and interactive service ensures users receive timely, professional guidance for fault resolution.',
  'aiAgentLibrary.agent.list.newEnergy.1.content.3.list.1':
    'When the system detects a power station fault alarm, it enters the conversation window without requiring user-initiated consultation, directly pushing fault information and handling suggestions.',
  'aiAgentLibrary.agent.list.newEnergy.2.title': 'Firmware Upgrade Assistant',
  'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.1':
    'Users need to manually request firmware upgrade services',
  'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.2':
    'Firmware upgrade needs identification for multilingual users',
  'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.3':
    'Device parameter configuration needs (collection frequency, language packs, etc.) are scattered',
  'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.4':
    'Lack of real-time status feedback during upgrade process',
  'aiAgentLibrary.agent.list.newEnergy.2.content.2.list.1':
    'This solution implements rapid response to firmware upgrade needs for multilingual users through intelligent semantic recognition and automated processes, providing real-time status feedback that significantly improves the efficiency and user experience of device upgrade services.',
  'aiAgentLibrary.agent.list.newEnergy.2.content.3.list.1':
    'I need to upgrade the firmware',
  'aiAgentLibrary.agent.list.newEnergy.2.content.3.list.2':
    'I want to upgrade the inverter to the latest version',
  'aiAgentLibrary.agent.list.newEnergy.3.title': 'Inverter Knowledge Expert',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.1':
    'Users lack in-depth understanding of inverter technical parameters and functions',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.2':
    'Fault diagnosis and maintenance information is scattered and difficult to access quickly',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.3':
    'Product selection decisions require professional knowledge support',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.4':
    'Installation and configuration processes for different inverter types are complex',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.5':
    'Technology updates rapidly, making it difficult for users to keep pace with latest developments',
  'aiAgentLibrary.agent.list.newEnergy.3.content.2.list.1':
    "This solution provides professional knowledge support for inverter selection, installation, configuration, fault diagnosis, and maintenance by building a comprehensive inverter knowledge base combined with intelligent semantic recognition technology. The system identifies users' specific needs and provides targeted technical answers and operational guidance, significantly enhancing user understanding and efficiency in using inverter products.",
  'aiAgentLibrary.agent.list.newEnergy.3.content.3.list.1':
    'I just purchased an inverter, how should I install it?',
  'aiAgentLibrary.agent.list.newEnergy.3.content.3.list.2':
    'A fault code is displayed, what does fault code 002 mean?',
  // 新能源 end
  // 电商&零售 start
  'aiAgentLibrary.agent.list.retail.1.title': 'Hassle-Free Returns Assistant',
  'aiAgentLibrary.agent.list.retail.1.content.1.list.1':
    'Return and exchange policies are complex and difficult to understand; customers are unsure if they qualify or what documents are required.',
  'aiAgentLibrary.agent.list.retail.1.content.1.list.2':
    'The application process is cumbersome, involving extensive forms and document uploads.',
  'aiAgentLibrary.agent.list.retail.1.content.1.list.3':
    'The status of returns and exchanges is unclear, with unknown refund or replacement timelines, requiring customers to follow up repeatedly.',
  'aiAgentLibrary.agent.list.retail.1.content.2.list.1':
    'Simplifies the complex return and exchange process into a conversational service. The agent proactively guides customers through applications, automates most steps, and provides real-time status updates.',
  'aiAgentLibrary.agent.list.retail.1.content.3.list.1':
    'I want to return my purchase.',
  'aiAgentLibrary.agent.list.retail.1.content.3.list.2':
    'Please help me return what I bought.',
  'aiAgentLibrary.agent.list.retail.2.title':
    'Intelligent Order Status & Logistics Tracker',
  'aiAgentLibrary.agent.list.retail.2.content.1.list.1': `"Where is my order?" (WISMO) is the most common inquiry in e-commerce support. Customers have to manually copy tracking numbers to check on third-party sites, and logistics updates (e.g., "Arrived at sorting facility") are often cryptic. During delays, customers become anxious and are often left without knowing the reason.`,
  'aiAgentLibrary.agent.list.retail.2.content.2.list.1':
    'This agent proactively provides customers with easy-to-understand order and shipping updates. It translates technical logistics terms into plain language and, in case of exceptions like delays or delivery failures, proactively explains the cause and offers solutions, converting customer anxiety into trust.',
  'aiAgentLibrary.agent.list.retail.2.content.3.list.1': 'Track my order.',
  'aiAgentLibrary.agent.list.retail.2.content.3.list.2': 'Where is my package?',
  'aiAgentLibrary.agent.list.retail.3.title':
    'Intelligent Cart Recovery Advisor',
  'aiAgentLibrary.agent.list.retail.3.content.1.list.1':
    'Customers add items to their cart but do not complete the purchase due to various reasons (price hesitation, shipping costs, product doubts, or distractions).',
  'aiAgentLibrary.agent.list.retail.3.content.1.list.2':
    'Traditional recovery methods (such as email reminders) are limited and fail to address the root concerns of customers.',
  'aiAgentLibrary.agent.list.retail.3.content.2.list.1':
    'After a period of cart abandonment, initiates a friendly, non-intrusive conversation via instant messaging platforms (e.g., web chat, WhatsApp) to understand the reasons for abandonment and offer personalized solutions (such as coupons, answering questions, or alleviating concerns) to encourage order completion.',
  'aiAgentLibrary.agent.list.retail.3.content.3.list.1':
    'Proactively sends a message to the user after a period of cart abandonment.',
  // 电商&零售 end
  // 制造 start
  //     英语 (English)
  'aiAgentLibrary.agent.list.manufacturing.1.title':
    ' Intelligent Device Fault Reporting Assistant',
  'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.1':
    ' <b>Inefficient and Inaccurate Information Transfer:</b> When reporting faults via phone or email, customers struggle to accurately describe technical issues, often omitting key details such as device serial numbers or error codes.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.2':
    ' <b>High Communication Costs:</b> Service teams must repeatedly follow up to obtain complete information, resulting in long diagnostic cycles and customer anxiety.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.3':
    ' <b>Initial Misjudgment:</b> Relying solely on verbal descriptions can lead to misjudgment of fault severity or type, resulting in dispatching the wrong technician or spare parts.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.4':
    ' <b>No Immediate Feedback:</b> After submitting a fault report, customers receive no timely updates and are left uncertain about the status or resolution timeline.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.2.list.1':
    'Provides an interactive, intelligent fault reporting workflow. Through conversational guidance, it helps customers provide all necessary information in a structured manner, including device model, fault symptoms, and operating environment. The agent understands natural language descriptions, automatically extracts key information, performs preliminary classification and priority assessment based on fault type, and generates comprehensive, focused, high-quality service tickets.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.3.list.1':
    'I want to report a fault.',
  'aiAgentLibrary.agent.list.manufacturing.2.title':
    'Manufacturing AI Knowledge Assistant',
  'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.1':
    ' <b>Knowledge Silos:</b> Critical information is scattered across PDF manuals, Word documents, CAD drawings, the minds of experienced workers, and outdated intranet systems, making it difficult to locate and utilize.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.2':
    ' <b>Low Search Efficiency:</b> Traditional keyword searches cannot understand the context of technical terms; searching for "bearing replacement" may yield irrelevant purchase orders instead of maintenance guides.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.3':
    ' <b>Slow Onboarding:</b> New employees (especially technicians and engineers) require months to become familiar with equipment and processes, resulting in high training costs and reliance on senior staff for mentorship.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.4':
    ' <b>Expert Dependency:</b> The knowledge of a few key experts is not systematically captured; when they leave, valuable experience is lost, creating knowledge bottlenecks within the team.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.2.list.1':
    'Establishes a unified, conversational knowledge portal. This agent not only searches but also understands natural language queries, reads and synthesizes all types of internal documents (manuals, drawings, reports, best practices), and provides precise, source-traceable answers. It acts as a tireless, highly knowledgeable expert on all equipment and processes.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.1':
    'What are the differences between the G3VM-26M10, G3VM-26M11, and G3VM-66M models? How should I select the appropriate model for my application?',
  'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.2':
    'What is the difference between VSON(R) and VSON?',
  'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.3':
    'If I want to order SOP8 surface-mount relays for an automated production line, how are they packaged, and what is the Minimum Order Quantity (MOQ)?',
  // 制造 end
  // 立即体验
  'aiAgentLibrary.agent.list.button': 'Try Now',
};
