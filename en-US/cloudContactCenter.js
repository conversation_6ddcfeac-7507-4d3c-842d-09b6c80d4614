export default {
  'homePage.homePage': 'HomePage',
  'homePage.cloudContactCenter': 'Cloud Contact Center',
  'homePage.contactUs': 'Contact US',
  'homePage.freeTrial': 'Free Trial',
  'homePage.topContent':
    'Globalization, integration and intelligent customer contact center.',
  'homePage.tipsBtn': 'Free Trail!',
  'homePage.centerContentTopFont': 'Choose your customer service solution.',
  'homePage.centerContentBottomFont': 'Solution feature comparison.',
  'homePage.listData.name1': 'Basic',
  'homePage.listData.name2': 'Advanced',
  'homePage.listData.name3': 'Professional',
  'homePage.listData.name4': 'Enterprise',
  'homePage.listData.name5': 'Private Deployment',
  'homePage.listData.tips1': 'For startups',
  'homePage.listData.tips2': 'For medium-sized enterprises',
  'homePage.listData.tips3': 'For large enterprises',
  'homePage.listData.tips4': 'Provide enterprise-level support',
  'homePage.listData.top': 'Most popular',
  'homePage.listData.tipsBtn1': '(Within 10 agent)',
  'homePage.listData.tipsBtn2': '(3 to 20 agent)',
  'homePage.listData.tipsBtn3': '(Starting from 6 agents)',
  'homePage.listData.tipsBtn4': '(Starting from 6 agents)',
  'homePage.listData.content1.1': '100 Free AIGC uses per month',
  'homePage.listData.content2.1': '300 Free AIGC uses per month',
  'homePage.listData.content3.1': '1,500 Free AIGC uses per month',
  'homePage.listData.content4.1': 'Free AIGC',
  'homePage.listData.content1.2': 'Excluding Live Chat',
  'homePage.listData.content2.2': '1,000 Free Live Chat Sessions per Month',
  'homePage.listData.content3.2': '4,000 Free Live Chat Sessions per Month',
  'homePage.listData.content4.2': '6,000 Free Live Chat Sessions per Month',
  'homePage.listData.content1.3': '1 Free US Phone per Month',
  'homePage.listData.content2.3': '1 Free US Phone per Month',
  'homePage.listData.content3.3': '1 Free US Phone per Month',
  'homePage.listData.content4.3': '2 Free US Phone per Month',
  'homePage.listData.content1.4':
    '200 Free Inbound Minutes per Month (on US Number)',
  'homePage.listData.content2.4':
    '200 Free Inbound Minutes per Month (on US Number)',
  'homePage.listData.content3.4':
    '2,500 Free Inbound Minutes per Month (on US Number)',
  'homePage.listData.content4.4':
    '2,500 Free Inbound Minutes per Month (on US Number)',
  'homePage.listData.result1': '6 Basic Rights',
  'homePage.listData.result2': 'All the features of the basic version and...',
  'homePage.listData.result3':
    'All the features of the advanced version and...',
  'homePage.listData.result4':
    'All the features of the professional version and...',
  'homePage.listData.resultList.1.1': 'Ticket System Basic Functions',
  'homePage.listData.resultList.1.2': 'Customer Center Basic Functions',
  'homePage.listData.resultList.1.3': 'Centralized Workspace',
  'homePage.listData.resultList.1.4': 'Telephone Channel Access',
  'homePage.listData.resultList.1.5': 'AIGC Sentiment Analysis',
  'homePage.listData.resultList.1.6': 'AIGC One-Click Summary',
  'homePage.listData.resultList.2.1': 'Ticket System Advanced Functions',
  'homePage.listData.resultList.2.2': 'Ticket System Advanced Functions',
  'homePage.listData.resultList.2.3': 'AIGC Translation',
  'homePage.listData.resultList.2.4': 'AIGC Grammar Correction',
  'homePage.listData.resultList.2.5': 'AIGC Content Generation',
  'homePage.listData.resultList.2.6': 'AIGC Charges By Volume',
  'homePage.listData.resultList.2.7': 'Email Channel Access',
  'homePage.listData.resultList.2.8': 'WEB Chat Channel Access',
  'homePage.listData.resultList.2.9': 'Mobile APP Chat Channel Access',
  'homePage.listData.resultList.2.10': 'Report Center',
  'homePage.listData.resultList.3.1': 'Ticket System All Functions',
  'homePage.listData.resultList.3.2': 'Customer Center All Functions',
  'homePage.listData.resultList.3.3': 'Social Media Channel Integration',
  'homePage.listData.resultList.3.3.1': 'Online voice/Online video',
  'homePage.listData.resultList.3.3.2': 'FAQ chatbot',
  'homePage.listData.resultList.3.4':
    'AIGC Knowledge Base Professional Edition + AIGC Intelligent Customer Service (Server-side)',
  'homePage.listData.resultList.3.5': 'Customer Data Extension Field Settings',
  'homePage.listData.resultList.3.6': 'More Customizable Ticket Settings',
  'homePage.listData.resultList.3.7': 'Standard IVR',
  'homePage.listData.resultList.3.8': 'Agent Management',
  'homePage.listData.resultList.4.1': 'AIGC Knowledge Base Enterprise Edition',
  'homePage.listData.resultList.4.2':
    'Desensitization of sensitive information data',
  'homePage.listData.resultList.4.3': 'Historical data archiving and recovery',
  'homePage.listData.resultList.4.4': 'Data stored for 2 years',
  'homePage.data.key.1.no': 'Agent dashboard',
  'homePage.data.key.2.no': 'Agent Centralized Workspace',
  'homePage.data.key.2.0.1.no': 'Agent AIGC Assistant',
  'homePage.data.key.3.no': 'Phone Channel',
  'homePage.data.key.4.no': 'Email Channel',
  'homePage.data.key.5.no': 'WhatsApp Channel',
  'homePage.data.key.6.no': 'Intelligent Customer Service + Live Chat',
  'homePage.data.key.7.no': 'Automatic Tickets / Manual Tickets',
  'homePage.data.key.8.no': 'Bot Tickets',
  'homePage.data.key.9.no': 'Customer Information',
  'homePage.data.key.10.no': 'Knowledge Base',
  'homePage.data.key.11.no': 'Dashboard',
  'homePage.data.key.12.no': 'Custom Customer Information',
  'homePage.data.key.13.no': 'Ticket Settings',
  'homePage.data.key.14.no': 'Platform User Management',
  'homePage.data.key.15.no': 'Agent Management',
  'homePage.data.key.16.no': 'Channel Configuration',
  'homePage.data.key.17.no': 'Message Notifications',
  'homePage.data.key.18.no': 'Preference Settings',
  'homePage.data.key.19.no': 'Personal Information Modification',
  'homePage.data.key.20.no': 'Data Processing',
  'homePage.data.key.1.no5': 'Contact Sales',
  'homePage.data.key.2.children.21.no':
    'Omni-Channel Agent Workspace: Phone, email, SMS, web chat, app chat, online voice, online video, WhatsApp, etc. ',
  'homePage.data.key.2.children.22.no': 'Omni-Channel New Message Notification',
  'homePage.data.key.2.children.23.no':
    'Incoming calls, incoming messages auto-generate tickets',
  'homePage.data.key.2.children.24.no': 'Manually create tickets',
  'homePage.data.key.2.children.25.no': 'Search all my tickets',
  'homePage.data.key.2.children.26.no': 'Agent work status freely switchable',
  'homePage.data.key.2.children.27.no': 'Support SSO',
  'homePage.data.key.2.children.28.no': 'Create tasks',
  'homePage.data.key.2.children.29.no':
    'Auto-pop customer information, automatically record customer information',
  'homePage.data.key.2.children.30.no': `View customer's historical tickets`,
  'homePage.data.key.2.children.31.no': 'Modify customer information',
  'homePage.data.key.2.children.32.no': 'Routing, queue configuration',
  'homePage.data.key.2.children.33.no': 'Intelligent queuing mechanism',
  'homePage.data.key.2.children.34.no': 'Define working hours',
  'homePage.data.key.2.children.35.no':
    'Limit number of concurrent online chats for agents',
  'homePage.data.key.2.children.36.no': 'AIGC dialogue summary',
  'homePage.data.key.2.0.1.children.21.no': 'CoCo Online: AIGC Translation',
  'homePage.data.key.2.0.1.children.22.no':
    'CoCo Online: AIGC Grammar Correction',
  'homePage.data.key.2.0.1.children.23.no':
    'CoCo Online: AIGC Email Content Formalization',
  'homePage.data.key.2.0.1.children.24.no':
    'CoCo Online: AIGC Email Content Generation',
  'homePage.data.key.2.0.1.children.25.no': 'CoCo Online: Freestyle Chat',
  'homePage.data.key.2.0.1.children.26.no': 'AIGC Sentiment Analysis',
  'homePage.data.key.2.0.1.children.27.no': 'AIGC One-Click Summarization',
  'homePage.data.key.2.0.1.children.28.no': 'Intelligent Matching',
  'homePage.data.key.2.0.1.children.29.no': 'AIGC Intelligent Knowledge Base',
  'homePage.data.key.2.0.1.children.30.no': 'Agent Quick Replies',
  'homePage.data.key.tips1': 'Pay As You Go',
  'homePage.data.key.3.children.31.no':
    'Call with support for toll-free, DID, and UIFN',
  'homePage.data.key.3.children.32.no': 'Call Forwarding, Number Porting',
  'homePage.data.key.3.children.33.no': 'Customizable IVR',
  'homePage.data.key.3.children.34.no': 'Satisfaction Survey',
  'homePage.data.key.3.children.35.no': 'Callback',
  'homePage.data.key.3.children.36.no': 'Caller ID Display',
  'homePage.data.key.3.children.37.no': 'Automatic Call Recording',
  'homePage.data.key.3.children.37001.no': 'Automatic Call Transcription',
  'homePage.data.key.3.children.38.no': 'Agent Call Transfer',
  'homePage.data.key.3.children.39.no': 'Automatic Phone Ticket Logging',
  'homePage.data.key.tips2': 'Standard Configuration',
  'homePage.data.key.tips3': 'Customizable',
  'homePage.data.key.tips4': 'Unlimited',
  'homePage.data.key.tips6': 'Document Quantity Limit',
  'homePage.data.key.4.children.41.no': 'Email Channel Integration',
  'homePage.data.key.4.children.42.no': 'Email Attachment Support',
  'homePage.data.key.4.children.41.no.1': 'Receive Emails, Send Emails',
  'homePage.data.key.4.children.42.no.1': 'Email Response within Minutes',
  'homePage.data.key.4.children.43.no': 'Email Ticket Auto-Merge Support',
  'homePage.data.key.4.children.44.no': 'Automatic Email Ticket Logging',
  'homePage.data.key.5.children.51.no': 'WhatsApp Channel Integration',
  'homePage.data.key.5.children.52.no': 'Support for FAQ Chatbot',
  'homePage.data.key.5.children.53.no': 'Support for AIGC Intelligent Chatbot',
  'homePage.data.key.5.children.54.no': 'Automatic Customer Language Detection',
  'homePage.data.key.5.children.55.no': 'WhatsApp Chat to Human Agent Handoff',
  'homePage.data.key.5.children.56.no': 'Display of Queued Agents',
  'homePage.data.key.5.children.57.no':
    'Display of Queue for Customer to Human Agent Handoff',
  'homePage.data.key.5.children.57001.no': 'WhatsApp Channel Configuration',
  'homePage.data.key.5.children.58.no': 'Unified Call and Chat Queuing',
  'homePage.data.key.5.children.59.no': 'Auto-Merge for Multiple Chat Tickets',
  'homePage.data.key.5.children.591111.no': 'Automatic WhatsApp Ticket Logging',
  'homePage.data.key.6.children.61.no': 'Web Chat Channel Integration',
  'homePage.data.key.6.children.62.no':
    'Mobile Chat Channel Integration (Mobile App H5 Embedded)',
  'homePage.data.key.6.children.63.no':
    'Support for User Basic Information Collection',
  'homePage.data.key.6.children.64.no':
    'Configurable UI (Company Logo, Color Scheme, etc.)',
  'homePage.data.key.6.children.65.no':
    'Support for Automatic Browser Chat Invitation',
  'homePage.data.key.6.children.66.no': 'Configurable Welcome Message',
  'homePage.data.key.6.children.67.no': 'Display Email Channel Entry',
  'homePage.data.key.6.children.68.no': 'Display WhatsApp Channel Entry  ',
  'homePage.data.key.6.children.69.no':
    'Display Historical Conversations for Customers',
  'homePage.data.key.6.children.691.no': 'Support FAQ Chatbot',
  'homePage.data.key.6.children.692.no': 'Support AIGC Intelligent Chatbot',
  'homePage.data.key.6.children.693.no':
    'Automatic Customer Language Detection',
  'homePage.data.key.6.children.694.no':
    'General Language Support including but not limited to: English, Simplified Chinese, Traditional Chinese, Japanese, Korean, German, Spanish, French, Arabic, Russian, Portuguese, etc.',
  'homePage.data.key.6.children.695.no': 'Automatic Multi-Language Adaptation',
  'homePage.data.key.6.children.696.no':
    'Support for Attachment Upload (Images, Videos, and Other Files), Emoji Input, and over 200MB Attachments',
  'homePage.data.key.6.children.697.no': 'Customizable IVR',
  'homePage.data.key.6.children.698.no': 'Chat-to-Human Handoff Anytime',
  'homePage.data.key.6.children.699.no':
    'Display Queue for Customer to Human Handoff',
  'homePage.data.key.6.children.6991.no': 'Agent Chat Transfer Support',
  'homePage.data.key.6.children.6992.no': 'Online Voice Call Support  ',
  'homePage.data.key.6.children.6993.no': 'Online Video Call Support',
  'homePage.data.key.6.children.6994.no': 'Automatic Post-Chat Survey ',
  'homePage.data.key.6.children.6995.no':
    'Low-Code Integration into Custom System or Shopify',
  'homePage.data.key.6.children.6996.no': 'Two Window Size Options',
  'homePage.data.key.6.children.6997.no': 'Unified Call and Chat Queuing',
  'homePage.data.key.6.children.6998.no':
    'Auto-Merge for Multiple Chat Tickets  ',
  'homePage.data.key.6.children.6999.no': 'Automatic Live Chat Ticket Logging',
  'homePage.data.key.7.children.71.no':
    'Omni-Channel Ticket Centralized Management',
  'homePage.data.key.7.children.72.no':
    'Incoming Calls, Messages Auto-Generate Tickets',
  'homePage.data.key.8.children.721.no': 'Manually Create Tickets',
  'homePage.data.key.7.children.73.no': 'My Tickets',
  'homePage.data.key.7.children.74.no': 'My Pending Tickets',
  'homePage.data.key.7.children.75.no': 'My Resolved Tickets',
  'homePage.data.key.7.children.751.no': 'Overdue Tickets',
  'homePage.data.key.7.children.76.no': `Tickets I'm Following`,
  'homePage.data.key.7.children.77.no': 'Unassigned Tickets',
  'homePage.data.key.7.children.78.no': 'Tickets of My Group',
  'homePage.data.key.7.children.79.no': 'All Tickets',
  'homePage.data.key.7.children.80.no': 'Custom Query Conditions',
  'homePage.data.key.7.children.81.no': 'Supervisor Ticket Reminders',
  'homePage.data.key.7.children.83.no':
    'Supervisor Ticket Assignment, Reassignment, Agent Claim',
  'homePage.data.key.7.children.85.no': 'Associated Tickets',
  'homePage.data.key.7.children.86.no': 'Automatic Ticket Merging',
  'homePage.data.key.7.children.87.no': 'Direct Customer Contact',
  'homePage.data.key.7.children.89.no': 'Export Tickets to Excel',
  'homePage.data.key.7.children.94.no': 'AIGC Sentiment Analysis',
  'homePage.data.key.7.children.95.no': 'AIGC One-Click Summary',
  'homePage.data.key.8.children.81.no': 'Auto-Generated Bot Tickets',
  'homePage.data.key.8.children.83.no':
    'Display Bot Ticket Content on Agent Handover',
  'homePage.data.key.9.children.91.no':
    'Omni-Channel Centralized Customer Information Management',
  'homePage.data.key.9.children.92.no': 'Auto-Create New Customer Information',
  'homePage.data.key.9.children.93.no': 'Manually Add Customer Information',
  'homePage.data.key.9.children.94.no': 'Bulk Import Customer Information',
  'homePage.data.key.9.children.95.no': 'Export Customer Information',
  'homePage.data.key.9.children.97.no': 'Direct Customer Contact',
  'homePage.data.key.9.children.98.no': 'Customer Consultation History',
  'homePage.data.key.9.children.99.no': 'Tag Customers, Search by Tag',
  'homePage.data.key.9.children.101.no': 'Customer Grouping Management',
  'homePage.data.key.9.children.102.no':
    'View All Historical Tickets for Customer',
  'homePage.data.key.10.children.101.no':
    'Enterprise AIGC Intelligent Knowledge Base (Internal)',
  'homePage.data.key.10.children.102.no':
    'AIGC Intelligent Online Customer Knowledge Base (External)',
  'homePage.data.key.10.children.103.no':
    'Document Knowledge Base Multi-Format Bulk Upload Support',
  'homePage.data.key.10.children.104.no':
    'Document Knowledge Base AIGC Integration Support',
  'homePage.data.key.10.children.105.no': 'FAQ Knowledge Base',
  'homePage.data.key.10.children.105.no111111': 'AIGC Knowledge Base',
  'homePage.data.key.10.children.106.no':
    'FAQ Knowledge Base Text, Image, Video Support',
  'homePage.data.key.10.children.107.no':
    'AIGC Multiple Question Generation for FAQ',
  'homePage.data.key.10.children.108.no': 'Synonym Definitions',
  'homePage.data.key.11.children.111.no': 'Agent Dashboard',
  'homePage.data.key.11.children.112.no': 'Supervisor Dashboard',
  'homePage.data.key.11.children.113.no': 'Admin Dashboard',
  'homePage.data.key.12.children.121.no':
    'Customer Information Extended Fields Setting',
  'homePage.data.key.13.children.131.no': 'Ticket Extended Fields Setting',
  'homePage.data.key.13.children.132.no': 'Ticket SLA Setting',
  'homePage.data.key.13.children.133.no': 'Ticket Type Definition',
  'homePage.data.key.13.children.134.no': 'Ticket Merge Setting',
  'homePage.data.key.14.children.141.no':
    'Platform User Addition & Permission Assignment',
  'homePage.data.key.14.children.142.no': 'Platform User Invitation',
  'homePage.data.key.14.children.143.no': 'Platform User Deactivation',
  'homePage.data.key.14.children.144.no': 'Platform Role Selection',
  'homePage.data.key.14.children.145.no': 'SSO Support',
  'homePage.data.key.17.children.171.no':
    'Transfer Reminders, Assignment Reminders',
  'homePage.data.key.17.children.172.no': 'Ticket Reminder',
  'homePage.data.key.17.children.173.no': 'Ticket Escalation Reminder',
  'homePage.data.key.17.children.174.no': 'Overdue SLA Ticket Reminder',
  'homePage.data.key.17.children.175.no': 'Other Message Reminders',
  'homePage.data.key.15.children.151.no': 'Agent management',
  'homePage.data.key.16.children.161.no': 'Phone Channel Support',
  'homePage.data.key.16.children.162.no': 'Email Channel Support',
  'homePage.data.key.16.children.163.no': 'Web Channel Support',
  'homePage.data.key.16.children.164.no': 'App Channel Support',
  'homePage.data.key.16.children.165.no': 'Web Live Video Channel Support',
  'homePage.data.key.16.children.166.no': 'App Live Video Channel Support',
  'homePage.data.key.16.children.167.no': 'WhatsApp Channel Support',
  'homePage.data.key.18.children.181.no': 'Time Zone Setting',
  'homePage.data.key.18.children.182.no':
    'Language Setting (Multi-Language Support)',
  'homePage.data.key.19.children.191.no':
    'Avatar, Change Password, Personal Information',
  'homePage.data.key.20.children.201.no': 'Sensitive Information Data Masking',
  'homePage.data.key.20.children.202.no':
    'Historical Data Archiving and Restoration',
  'homePage.data.key.20.children.203.no': 'Data Retention Period',
  'homePage.data.key.20.children.tips.180': '180 Days',
  'homePage.data.key.20.children.tips.365': '365 Days',
  'homePage.data.key.20.children.tips.730': '730 Days',
  'homePage.button.name': 'Free Experience',
  'homePage.tipsBtn1.mobile': 'For more details',
  'homePage.tipsBtn2.mobile': 'Free Trail',
};
