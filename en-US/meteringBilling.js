export default {
  //日志
  'workTable.setting.loggor.title': 'Log',
  'workTable.setting.loggor.button': 'Download log',
  // 计量计费菜单
  'metering.billing': 'Billing',
  'metering.billing.1': 'Overview',
  'metering.billing.2': 'External AIGC package',
  'metering.billing.3': 'Agent Copilot AIGC package',
  'metering.billing.4': 'Voice bot package',
  'metering.billing.5': 'Call charges',
  //座席辅助AIGC套餐包
  'agent.AIGC.package.title': 'Agent Copilot AIGC Package',
  'agent.AIGC.package.echart.title': 'Agent Copilot AIGC usage trend chart',
  'agent.AIGC.package.table.type.1': 'Gift',
  'agent.AIGC.package.table.type.2': 'Enterprise Purchase',
  //电话机器人套餐包
  'phone.robot.package.title': 'Voice Bot Package',
  'phone.robot.package.echart.left.title': 'Trend chart of Voice bot usage',
  'phone.robot.package.echart.right.title':
    'Percentage of minutes used by each phone number',
  'phone.robot.package.table.1': 'Phone number',
  'phone.robot.package.table.2': 'Minutes of usage',
  'phone.robot.package.table.3': 'Minutes',
  'phone.robot.package.table.3.tips': 'Remaining available minutes',
  //告警规则
  'alarm.rule.title': 'Alert Rules',
  'alarm.rule.title.step.1': 'Alert rules',
  'alarm.rule.title.step.2': 'Alert notification method',
  'alarm.rule.title.step.1.1': 'Call charges alert',
  'alarm.rule.title.step.1.1.content':
    'When the remaining balance of call charges is less than or equal to {number} yuan, {alarm} is issued.',
  'alarm.rule.title.step.alarm.yellow': 'orange alert',
  'alarm.rule.title.step.alarm.red': 'red alert',
  'alarm.rule.title.step.1.2': 'External AIGC remaining quota alert',
  'alarm.rule.title.step.1.2.content':
    'When the remaining number of calls for the external AIGC is less than or equal to {number}, {alarm} is issued.',
  'alarm.rule.title.step.1.3': 'Agent Copilot AIGC remaining quota alert',
  'alarm.rule.title.step.1.3.content':
    'When the remaining number of calls for the Agent Copilot AIGC is less than or equal to {number}, {alarm} is issued.',
  'alarm.rule.title.step.1.4': 'Voice bot remaining minutes alert',
  'alarm.rule.title.step.1.4.content':
    'When the remaining minutes of the Voice bot are less than or equal to {number}, {alarm} is issued.',
  'alarm.rule.title.step.2.checkbox': 'Turn on email notifications',
  'alarm.rule.title.step.2.checkbox.tips':
    'When an alert occurs, an email notification will be sent to the following email addresses',
  'alarm.rule.title.step.2.select': 'Notify the user',
  'alarm.rule.title.step.2.select.p': 'Select user',
  // 计量计费概览页面
  'over.view.title': 'Overview',
  'over.view.version.1': 'Basic',
  'over.view.version.2': 'Growth',
  'over.view.version.3': 'Professional',
  'over.view.version.4': 'Enterprise',
  'over.view.overview.telephone.telephone': 'Call charges overview',
  'over.view.user.num': 'Number of users: ',
  'over.view.user.num.unit': '',
  'over.view.user.management': 'User management',
  'over.view.ai.agent.num': 'Number of AI Agent: ',
  'over.view.ai.agent.management': 'AI Agent management',
  'over.view.overview.telephone.charges': 'Call charges overview',
  'over.view.current.balance': 'Current balance',
  'over.view.go.recharge': 'Recharge',
  'over.view.user.num.unit.yuan': 'yuan',
  'over.view.user.num.unit.zi': 'word',
  'over.view.consumer.trends.june': 'Spending trend over the last 6 months',
  'over.view.knowledge.base.space': 'Knowledge base storage used',
  'over.view.knowledge.base.space.1': 'Knowledge base storage used',
  'over.view.knowledge.base.num': 'Number of knowledge bases: ',
  'over.view.external.intelligent.agent.usage': 'External AIGC usage',
  'over.view.external.intelligent.agent.usage.1': 'Agent Copilot AIGC usage',
  'over.view.external.intelligent.agent.usage.2': 'Voice bot available minutes',
  // 外部智能体AIGC套餐包
  'external.intelligent.agent.AIGC.package.title': 'External AI AIGC Package',
  'external.intelligent.agent.AIGC.package': 'AIGC package',
  'external.intelligent.agent.AIGC.package.data.analysis': 'Data analysis',
  'external.intelligent.agent.AIGC.package.select.time': 'Select time',
  'external.intelligent.agent.AIGC.package.select.time.placeholder':
    'Please select time',
  'external.intelligent.agent.AIGC.package.select.time.one.month':
    'in the past month',
  'external.intelligent.agent.AIGC.package.select.time.six.month':
    'in the past six months',
  'external.intelligent.agent.AIGC.package.select.time.one.year':
    'in the past year',
  'external.intelligent.agent.AIGC.package.select.time.customize': 'custom',
  'external.intelligent.agent.AIGC.package.time.frame': 'Time range',
  'external.intelligent.agent.AIGC.package.call.frequency.trend.chart':
    'Trend chart of AIGC calls',
  'external.intelligent.agent.AIGC.package.proportion.channel.call.frequency':
    'Percentage of AIGC calls across different channels',
  'external.intelligent.agent.AIGC.package.call.frequency.details':
    'Usage details',
  'external.intelligent.agent.AIGC.package.call.frequency.details.1':
    'Usage details',
  'external.intelligent.agent.AIGC.package.table.date': 'Date',
  'external.intelligent.agent.AIGC.package.table.channel': 'Channel',
  'external.intelligent.agent.AIGC.package.table.number.calls': 'Count',
  'external.intelligent.agent.AIGC.package.remaining.callable.times':
    'Remaining available quota',
  'external.intelligent.agent.AIGC.package.go.buy': 'Recharge',
  'external.intelligent.agent.AIGC.package.unit.times': ' ',
  'external.intelligent.agent.AIGC.package.select.buy.time': 'Purchase time',
  'external.intelligent.agent.AIGC.package.select.status': 'Status: ',
  'external.intelligent.agent.AIGC.package.select.status.in.effect': 'Active',
  'external.intelligent.agent.AIGC.package.select.status.expired': 'Expired',
  'external.intelligent.agent.AIGC.package.select.status.exhausted':
    'Exhausted',
  'external.intelligent.agent.AIGC.package.table.type': 'Type',
  'external.intelligent.agent.AIGC.package.table.expiration.date':
    'Expiration date',
  'external.intelligent.agent.AIGC.package.table.status': 'Status',
  'over.view.external.intelligent.agent.usage.title': 'Remaining AIGC Usage',
  'over.view.external.intelligent.agent.usage.1.title': 'Remaining AIGC Usage',
  'over.view.external.intelligent.agent.usage.2.title': 'Remaining minutes',
  // 话务费
  'telephone.expenses.tab.1': 'Recharge history',
  'telephone.expenses.trend.telephone.charges': 'Call cost trend',
  'telephone.expenses.proportion.telephone.call.usage': 'Call cost usage ratio',
  'telephone.expenses.cost.details.title': 'Cost details',
  'telephone.expenses.table.fee.type': 'Fee type',
  'telephone.expenses.table.fee.type.placeholder': 'Select fee type',
  'customerInformation.table.nation.placeholder': 'Select country',
  'telephone.expenses.table.usage.unit.price': 'Unit price',
  'telephone.expenses.table.subtotal.expenses': 'Subtotal',
  'telephone.expenses.table.money.unit': 'USD',
  'phone.robot.package.table.1.placeholder': 'Enter phone number to search',
  'recharge.record.table.recharge.time': 'Recharge time',
  'recharge.record.table.recharge.amount': 'Recharge amount',
  'recharge.record.table.remaining.amount': 'Remaining balance',
  'phone.robot.package.table.1.system': 'System number',
  'phone.robot.package.table.1.system.placeholder': 'Select system number',
  'over.view.overview.telephone.telephone.balance': 'Current balance',
};
