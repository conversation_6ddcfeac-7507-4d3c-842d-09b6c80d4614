export default {
  'ai.agent.api.modal.title': 'API Management',
  'ai.agent.api.table.operation.add': 'Add',
  'ai.agent.api.table.key.placeholder': 'Please enter key',
  'ai.agent.api.table.value.placeholder': 'Please enter value',
  'ai.agent.api.table.description.placeholder': 'Please enter description',
  'ai.agent.api.table.description.title': 'Description',
  'ai.agent.api.table.operation.title': 'Operation',
  'ai.agent.api.search.placeholder': 'Search',
  'ai.agent.api.operation.add': 'Add API Interface',
  'ai.agent.api.table.empty.text': 'You have not added any API interfaces yet',
  'ai.agent.api.url.placeholder': 'Enter API interface address',
  'ai.agent.api.operation.test.btn': 'Test',
  'ai.agent.api.operation.save.btn': 'Save',
  'ai.agent.api.response.title': 'Test Result',
  'ai.agent.script.settings.not.empty.tips':
    'Please save the currently edited API',
  'ai.agent.api.url.placeholder.tips': 'Please enter the API interface address',
  'ai.agent.api.authentication.open': 'Enable API authentication',
  'ai.agent.api.authentication.type': 'API authentication type',
  'ai.agent.api.authentication.header.placeholder': 'Please enter Header',
  'ai.agent.api.authentication.apiKey.placeholder': 'Please enter API Key',
  'ai.agent.api.authentication.basic': 'Basic',
  'ai.agent.api.authentication.bearer': 'Bearer',
  'ai.agent.api.authentication.custom': 'Custom',
};
