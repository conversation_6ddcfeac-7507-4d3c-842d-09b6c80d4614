export default {
  'workRecord.customer.link.way': 'Customer contact information',
  'common.total.items': 'Total {total} items',
  'message.upload.size': 'The uploaded file size cannot exceed {fileSize}MB',
  'work.record.channel.name': 'Channel type',
  'work.record.time.range': 'Time range',
  'work.record.waiting.handle': 'Pending',
  'work.record.in.process': 'In progress',
  'work.record.resolved': 'Resolved',
  'work.record.id': 'Record ID',
  'work.record.customer.name': 'Customer name',
  'work.record.customer.link.way': 'Customer contact information',
  'work.record.settle.name': 'Agent name',
  'work.record.status': 'Status',
  'work.record.create.time': 'Create time',
  'work.record.resolve.time': 'Resolution time',
  'work.record.operation': 'Action',
  'work.record': 'Ticket',
  'work.record.detail': 'Details',
  'work.record.reply': 'Reply',
  'work.record.mark.as.in.process': 'Mark as in progress',
  'work.record.mark.as.resolved': 'Mark as resolved',
  'work.record.note': 'Note',
  'work.record.button.ok': 'Confirm',
  'work.record.button.cancel': 'Cancel',
  'work.record.customer.info': 'Customer information',
  'work.record.return': 'Return',
  'work.record.channel': 'Channel',
  'work.record.start.time': 'Start time',
  'work.record.button.save': 'Save',
  'work.record.recipient': 'Recipient',
  'work.record.caller': 'Caller',
  'work.record.voice.record': 'Voice recording',
  'work.record.tips.note.limit':
    'Please enter the note content, with a maximum of 2000 characters',
  'work.record.tips.customer.search.enter':
    'Please enter customer contact information and press Enter to search',
  'work.record.tips.note.settle.search.enter':
    'Please enter agent name and press Enter to search',
  'work.record.tips.note.keyword.enter':
    'Please enter information such as record ID, customer contact information, or dynamic column content and press Enter to search.',
  'work.record.tips.note.content.not.empty':
    'To save, the note must not be empty!',
  'work.record.tips.note.content.not.empty.string':
    'The note content cannot be an empty string!',
  'work.record.tips.note.content.num.limit':
    'The note content can have a maximum of 2000 characters!',
  'work.record.tips.content.not.empty.enter':
    'Please enter non-empty content and press Enter to search!',
  'work.record.tips.content.num.limit':
    'Content length cannot exceed 80 characters!',
  'work.record.tips.customer.link.way.not.empty.string':
    'The customer contact information input box cannot be an empty string!',
  'work.record.tips.settle.not.empty.string':
    'The agent name input box cannot be an empty string!',
  'work.record.tips.keyword.not.empty.string':
    'The search box content cannot be an empty string!',
  'work.record.tips.at.least.one.status': 'Please select at least one status!',
  'work.record.detail.contact.search.copy': 'Contact Search corresponds to',
  'work.record.reply.content.not.empty': 'Reply content cannot be empty!',
  'work.record.reply.success': 'Reply successful!',
  'work.record.detail.copy.success': 'Copied successfully!',
  'work.record.channel.type': 'Please select the channel type',
};
