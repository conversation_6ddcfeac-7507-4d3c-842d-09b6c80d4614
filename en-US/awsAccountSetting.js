export default {
  'awsAccountSetting.title': 'Bind AWS account',
  'awsAccountSetting.add.btn': 'Add AWS account',
  'awsAccountSetting.bind.status': 'Your account has been bound successfully',
  'awsAccountSetting.delete.btn': 'Delete',
  'awsAccountSetting.connect.list': 'AWS connect instance list',
  'awsAccountSetting.synConnect': 'Sync AWS connect instances',
  'awsAccountSetting.editor.btn': 'Edit',
  'awsAccountSetting.awsUserName': 'Account name',
  'awsAccountSetting.awsUserName.placeholder': 'Please enter the account name',
  'awsAccountSetting.awsUserId': 'Account ID',
  'awsAccountSetting.awsUserId.placeholder': 'Please enter the account ID',
  'awsAccountSetting.accessKey': 'Access Key ID',
  'awsAccountSetting.accessKey.placeholder': 'Please enter the access key',
  'awsAccountSetting.secretAccessKey': 'Secret Access Key',
  'awsAccountSetting.secretAccessKey.placeholder':
    'Please enter the secret access key',
  'awsAccountSetting.region': 'Region',
  'awsAccountSetting.region.placeholder': 'Please select a region',
  'awsAccountSetting.cancel.btn': 'Cancel',
  'awsAccountSetting.save.btn': 'Save',
  'awsAccountSetting.region.tips': 'Please select at least one region',
  'awsAccountSetting.regions.placeholder': 'Please select at least one region',
  'awsAccountSetting.secretAccessKey.tips':
    'Please enter the correct Secret Access Key',
  'awsAccountSetting.accessKey.tips': 'Please enter the correct Access Key ID',
  'awsAccountSetting.awsUserId.tips': 'Please enter the correct account ID',
  'awsAccountSetting.awsUserName.tips': 'Please enter the correct account name',
  'awsAccountSetting.iam.tips':
    'Generate an access key in IAM and enter it below，click',
  'awsAccountSetting.iam1.tips': 'View how to generate an access key?',
  'awsAccountSetting.pTitle.tips': 'Note: Only displayed in this system',
  'awsAccountSetting.pTable.tips':
    'Note: This operation only affects the display of data in this system and does not operate AWS console instances.',
  'awsAccountSetting.pTable.return': 'Return',
  'awsAccountSetting.disable.text': 'Do you confirm that you want to disable',
  'awsAccountSetting.disable.text1': 'instance?',
  'awsAccountSetting.setting.alias': 'Set alias',
  'awsAccountSetting.label.alias': 'Alias:',
  'awsAccountSetting.alias.placeholder': 'Please enter an alias',
  // 'studentManagement.altogether': 'Total {total} items',
  'awsAccountSetting.connectAlias.table': 'Instance alias',
  'awsAccountSetting.connectUrl.table': 'Access url',
  'awsAccountSetting.bound.table': 'Channel',
  'awsAccountSetting.createTime.table': 'Creation date',
  'awsAccountSetting.connectStatus.table': 'Status',
  'awsAccountSetting.operation.table': 'Actions',
  'awsAccountSetting.disable.table': 'Disable',
  'awsAccountSetting.alias.table': 'Set alias',
  'awsAccountSetting.bound.table.type1': 'Outbound',
  'awsAccountSetting.bound.table.type2': 'Inbound',
  'awsAccountSetting.bound.table.type3': 'In and outbound',
  'awsAccountSetting.connectStatus.table.type1': 'Active',
  'awsAccountSetting.connectStatus.table.type2': 'Disabled',
};
