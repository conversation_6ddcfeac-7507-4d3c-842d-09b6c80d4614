export default {
  'definition.synonyms.title': 'Terminology',
  'definition.synonyms.bidirectional.synonym': 'Bidirectional terminology:',
  'definition.synonyms.unidirectional.synonym': 'Unidirectional terminologys:',
  'definition.synonyms.usage.rules': 'Usage rules:',
  'definition.synonyms.bidirectional.synonym.tips1':
    "When a user uses any term, all associated terms will be applied together, including the user's original input term.",
  'definition.synonyms.bidirectional.synonym.tips2':
    'For example: If you configure "检索增强生成", "RAG", and "Retrieval Augmented Generation" as bidirectional terminology, when a user inputs any one of these terms, the system will recognize all three terms and apply them to search, translation, and knowledge query.',
  'definition.synonyms.unidirectional.synonym.tips1':
    'This term will map to the term on the right for understanding and processing.',
  'definition.synonyms.unidirectional.synonym.tips2':
    'For example: If you set the unidirectional terminology for "ML" as "Machine Learning", when a user inputs "ML", the system will understand it as "Machine Learning", but inputting "Machine Learning" will not associate it with "ML".',
  'definition.synonyms.usage.rules.tips1':
    'Terminology is applied in various scenarios such as knowledge query, real-time translation, and AIGC generating more Q&A.',
  'definition.synonyms.add.rule.btn': 'Add rule',
  'definition.synonyms.bidirectional.synonym.title':
    'Bidirectional terminology',
  'definition.synonyms.unidirectional.synonym.title':
    'Unidirectional terminology',
  'definition.synonyms.bidirectional.synonym.terms': 'Term:',
  'definition.synonyms.bidirectional.synonym.synonyms': 'Terminology:',
  'definition.synonyms.add.synonym.rules': 'Adding terminology rules',
  'definition.synonyms.editor.synonym.rules': 'Modify terminology rules',
  'definition.synonyms.delete.synonym.rules': 'Delete knowledge base',
  'definition.synonyms.modal.rule.type': 'Rule type',
  'definition.synonyms.modal.rule.type.required': 'Please select rule type',
  'definition.synonyms.delete.synonym.rules.tips':
    'Are you sure you want to delete this content?',
  'definition.synonyms.add.synonym.rules.number.tips': 'The most add 50 tags!',
  'definition.synonyms.add.synonym.rules.number.tips.20':
    'The most add 20 tags!',
  'definition.synonyms.add.synonym.rules.type.tips':
    "Special symbols only support inputting '&', '-', '_', '.', spaces, and the first character cannot be empty",
  'definition.synonyms.add.terms.synonym.rules.empty.tips':
    'A single direction terminology or terminology words/phrases cannot be empty',
  'definition.synonyms.add.bidirectional.rules.empty.tips':
    'The contents of Bidirectional terminology words cannot be empty',
  'definition.synonyms.upload.tips': 'Pulling down to load...',
  'definition.synonyms.upload.nodata.tips': 'There is no more~',
  'definition.synonyms.input.placeholder':
    'Please enter tags and press Enter. You can enter multiple tags.',
  'definition.synonyms.input.placeholder.100': 'Enter up to 100 characters.',
  'definition.synonyms.synchronize.btn':
    'Synchronize to document knowledge base',
  'definition.synonyms.synchronizing.btn': 'Syncing to document knowledge base',
  'definition.synonyms.synchronize.status.tips':
    'Synchronizing, Please check later',
  'definition.synonyms.usage.rules.use.tips.title':
    'Do not use terminology in the following scenarios: ',
  'definition.synonyms.usage.rules.use.tips.text':
    '1. Common english synonymous expressions, e.g., leader, head;',
  'definition.synonyms.usage.rules.use.tips.text1':
    '2. Typos, e.g., teh => the;',
  'definition.synonyms.usage.rules.use.tips.text2':
    '3. Morphological variations, such as noun plurals and possessives; adjective comparatives, e.g., good, better, best;',
  'definition.synonyms.usage.rules.use.tips.text3':
    '4. Unigram (word) is a stop word such as the World Health Team.',
};
