export default {
  'customerInformation.customerName': 'Last Name:',
  'customerInformation.customerName.placeholder':
    'Please enter Last Name and press Enter to search',
  'customerInformation.emailAddress': 'Email:',
  'customerInformation.emailAddress.placeholder':
    'Please enter email address and press Enter to search',
  'customerInformation.add.basicInformation.emailAddress.placeholder.1':
    'Please enter email address',
  'customerInformation.customerBusinessName': 'Customer business name:',
  'customerInformation.customerBusinessName.placeholder':
    'Please enter customer business name and press Enter to search',
  'customerInformation.sourceChannel': 'Source channel:',
  'customerInformation.searchPlaceholder':
    'Please enter customer information and press Enter to search',
  'customerInformation.customerInformation': ' Customer information',
  'customerInformation.customerInformation1': 'Customer information',
  'customerInformation.table.sureName': 'Last Name',
  'customerInformation.table.customerName': 'First Name',
  'customerInformation.table.customerCode': 'Customer number',
  'customerInformation.table.mailAddress': 'Email',
  'customerInformation.table.telephonePrefixId': 'Area code',
  'customerInformation.table.mobilePhoneNumber': 'Phone',
  'customerInformation.table.customerLabel': 'Tags',
  'customerInformation.table.ContactTime': 'Last contact time',
  'customerInformation.table.ContactInformation': 'Contact information',
  'customerInformation.table.nation': 'Country',
  'customerInformation.table.creationMethod': 'Creation method',
  'customerInformation.table.groupName': 'Group name',
  'customerInformation.table.sourceChannel': 'Source channel',
  'customerInformation.table.createTime': 'Create time',
  'customerInformation.table.address': 'Mailing address',
  'customerInformation.table.operation': 'Action',
  'customerInformation.table.contactCustomer': 'Contact customer',
  'customerInformation.table.customer.detail': 'Detail',
  'customerInformation.table.editor': 'Update',
  'customerInformation.add.basicInformation.customerID': 'member ID',
  'customerInformation.add.basicInformation.customerID.required':
    'Please enter member ID',
  'customerInformation.add.basicInformation.addGroup': 'Add group',
  'customerInformation.add.basicInformation.pattern':
    "Format is incorrect, only Chinese, English, numbers, and '.' are allowed",
  'customerInformation.add.basicInformation.maxlength':
    'Length cannot exceed 80 characters',
  'customerInformation.add.basicInformation.maxlength2':
    'Length cannot exceed 200 characters',
  'customerInformation.add.basicInformation.maxlength3':
    'Length cannot exceed 2000 characters',
  'customerInformation.add.basicInformation.placeholder': 'Please enter',
  'customerInformation.add.basicInformation.title': 'Basic information',
  'customerInformation.add.basicInformation.title2':
    'Customer social media information',
  'customerInformation.add.basicInformation.title3':
    'Customer extension information',
  'customerInformation.add.basicInformation.selectiveGrouping': 'Select group',
  'customerInformation.add.basicInformation.selectiveGrouping.placeholder':
    'Please select a group',
  'customerInformation.add.basicInformation.selectiveGrouping.required':
    'Please select a group',
  'customerInformation.add.basicInformation.lastname': 'Last name:',
  'customerInformation.add.basicInformation.lastname.placeholder':
    'Please enter the last name',
  'customerInformation.add.basicInformation.lastname.required':
    'Please enter the last name',
  'customerInformation.add.basicInformation.name': 'First name:',
  'customerInformation.add.basicInformation.name.placeholder':
    'Please enter the first name',
  'customerInformation.add.basicInformation.name.required':
    'Please enter the first name',
  'customerInformation.add.basicInformation.name.pattern':
    "Name format is incorrect, only Chinese, English, numbers, spaces, dashes, underscores and '.' are allowed",
  'customerInformation.add.basicInformation.name.maxlength':
    'Name length cannot exceed 200 characters',
  'customerInformation.add.basicInformation.sourceChannel.placeholder':
    'Please select the source channel',
  'customerInformation.add.basicInformation.channel.name':
    'Please select the channel name',
  'customerInformation.add.basicInformation.sourceChannel.required':
    'Please select the source channel',
  'customerInformation.add.basicInformation.dateBirth': 'Birthday:',
  'customerInformation.add.basicInformation.sex': 'Gender:',
  'customerInformation.add.basicInformation.sex.placeholder':
    'Please select gender',
  'customerInformation.add.basicInformation.contactNumber': 'Phone:',
  'customerInformation.add.basicInformation.contactNumber.placeholder':
    'Please enter the phone',
  'customerInformation.add.basicInformation.contactNumber.pattern':
    'Contact phone format is incorrect, only numbers are allowed',
  'customerInformation.add.basicInformation.contactNumber.pattern1':
    'Password must contain at least two of the following: numbers, English, symbols; Symbol range ~!@#$%^&*()_+<>?.,',
  'customerInformation.add.basicInformation.contactNumber.maxlength':
    'Contact phone length cannot exceed 40 characters',
  'customerInformation.add.basicInformation.emailAddress': 'Email:',
  'customerInformation.add.basicInformation.emailAddress.placeholder':
    'Please enter the email address',
  'customerInformation.add.basicInformation.Address.placeholder':
    'Please select the region',
  'customerInformation.add.basicInformation.emailAddress.pattern':
    'Email address format is incorrect',
  'customerInformation.add.basicInformation.companyName': 'Company name:',
  'customerInformation.add.basicInformation.companyName.placeholder':
    'Please enter the company name',
  'customerInformation.add.basicInformation.position': 'Job title:',
  'customerInformation.add.basicInformation.position.placeholder':
    'Please enter the job title',
  'customerInformation.add.basicInformation.mailingAddress': 'Mailing address',
  'customerInformation.add.basicInformation.mailingAddress.placeholder':
    'Please enter the mailing address',
  'customerInformation.add.basicInformation.orderAddress': 'Billing address',
  'customerInformation.add.basicInformation.orderAddress.placeholder':
    'Please enter the billing address',
  'customerInformation.add.basicInformation.deliveryAddress':
    'Delivery address',
  'customerInformation.add.basicInformation.deliveryAddress.placeholder':
    'Please enter the delivery address',
  'customerInformation.add.basicInformation.otherAddress': 'Other address',
  'customerInformation.add.basicInformation.otherAddress.placeholder':
    'Please enter other address',
  'customerInformation.add.basicInformation.remark': 'Remark',
  'customerInformation.add.basicInformation.remark.placeholder':
    'Please enter the remark',
  'customerInformation.add.basicInformation.return.confirm':
    'Clicking return will lose the current modified data, confirm return?',
  'customerInformation.add.basicInformation.button.return': 'Return',
  'customerInformation.add.basicInformation.button.save': 'Save',
  'customerInformation.add.basicInformation.button.update': 'Update',
  'customerInformation.modal.basicInformation.tips': 'Prompt',
  'customerInformation.modal.basicInformation.button.ok':
    'View customer information',
  'customerInformation.modal.basicInformation.button.cancel': 'Cancel',
  'customerInformation.contactCustomer.info.title': 'Customer information',
  'customerInformation.contactCustomer.info.customerName': 'Customer name:',
  'customerInformation.contactCustomer.info.telephone': 'Phone:',
  'customerInformation.contactCustomer.info.emailAddress': 'Email:',
  'customerInformation.contactCustomer.info.channelName': 'Channel:',
  'customerInformation.contactCustomer.info.customerGroupName': 'Group:',
  'customerInformation.contactCustomer.info.mailingAddress': 'Mailing address:',
  'customerInformation.contactCustomer.channel.placeholder':
    'Please select a channel',
  'customerInformation.contactCustomer.subject.placeholder':
    'Please enter the email subject',
  'customerInformation.contactCustomer.send.button': 'Send',
  'customerInformation.contactCustomer.table.workRecordId': 'Record ID',
  'customerInformation.contactCustomer.table.channelName': 'Channel name',
  'customerInformation.contactCustomer.table.customerContactInfo':
    'Customer contact information',
  'customerInformation.contactCustomer.table.userName': 'Agent name',
  'customerInformation.contactCustomer.table.status': 'Status',
  'customerInformation.contactCustomer.table.createTime': 'Create time',
  'customerInformation.contactCustomer.table.resolveTime': 'Resolution time',
  'customerInformation.contactCustomer.button.send': 'Send',
  'customerInformation.contactCustomer.record': 'Communication History',
  'customerInformation.name.not.empty':
    'The customer fist name at line {lineNum} cannot be empty',
  'customerInformation.name.length.limit':
    'The customer fist name at line {lineNum} cannot exceed 200 characters',
  'customerInformation.name.input.limit':
    "The customer fist name at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.last.name.not.empty':
    'The customer last name at line {lineNum} cannot be empty',
  'customerInformation.last.name.length.limit':
    'The customer last name at line {lineNum} cannot exceed 200 characters',
  'customerInformation.last.name.input.limit':
    "The customer last name at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.group.not.empty':
    'The group name at line {lineNum} cannot be empty',
  'customerInformation.channel.not.empty':
    'The source channel at line {lineNum} cannot be empty',
  'customerInformation.phone.input.limit':
    'The mobile phone number at line {lineNum} has an incorrect format, only numbers are allowed',
  'customerInformation.phone.length.limit':
    'The mobile phone number at line {lineNum} cannot exceed 40 characters',
  'customerInformation.phone.prefix.limit':
    'The international dialing code at line {lineNum} is incorrect, please change it',
  'customerInformation.company.input.limit':
    "The company name at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.company.length.limit':
    'The company name at line {lineNum} cannot exceed 80 characters',
  'customerInformation.post.input.limit':
    "The job title at line {lineNum} can only contain Chinese, English, numbers, and '.' ",
  'customerInformation.post.length.limit':
    'The position at line {lineNum} cannot exceed 80 characters',
  'customerInformation.mailingAddress.input.limit':
    "The mailing address at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.mailingAddress.length.limit':
    'The mailing address at line {lineNum} cannot exceed 200 characters',
  'customerInformation.orderAddress.input.limit':
    "The billing address at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.orderAddress.length.limit':
    'The billing address at line {lineNum} cannot exceed 200 characters',
  'customerInformation.deliveryAddress.input.limit':
    "The delivery address at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.deliveryAddress.length.limit':
    'The delivery address at line {lineNum} cannot exceed 200 characters',
  'customerInformation.otherAddress.input.limit':
    "The other address at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.otherAddress.length.limit':
    "he other address at line {lineNum} cannot exceed 200 characters; The remark at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.remark.input.limit':
    "The remark at line {lineNum} can only contain Chinese, English, numbers, and '.'",
  'customerInformation.remark.length.limit':
    'The remark at line {lineNum} cannot exceed 200 characters',
  'customerInformation.repeat.phone.tips':
    'There are duplicate mobile phone numbers in the file: {repeatPhone}',
  'customerInformation.repeat.email.tips':
    'There are duplicate emails in the file: {repeatEmail}',
  'customerInformation.batchImport': 'Batch import',
  'customerInformation.export': 'Export',
  'customerInformation.download.template': 'Download template',
  'customerInformation.add.basicInformation.customerLevel': 'Level:',
  'customerInformation.add.basicInformation.customerLevel.placeholder':
    'Please select customer level',
  'customerInformation.add.basicInformation.customerLevel.required':
    'Please select customer level',
  'customerInformation.add.basicInformation.customer.language': 'Language',
  'customerInformation.add.basicInformation.tags': 'Tag:',
  'customerInformation.add.basicInformation.tags.placeholder':
    'Please input the tag and press Enter',
  'customerInformation.modal.batch.change.grouping': 'Batch change group',
  'customerInformation.modal.batch.import': 'Batch import',
  'hotWord.length': 'The length of the tag is too long',
  'customerInformation.upload.file':
    'Only supports uploading one file, click on <b>{US}</b>',
  'knowledge.QA.upload.file.request':
    '1. Excel format supports *. xls, * XLSX in two formats',
  'customerInformation.upload.btn': 'Drag and drop files here or<b>{US}</b>',
  'customerInformation.upload.download': 'Click to download',
  'customerInformation.upload.file.1': 'Document description: ',
  'customerInformation.option.error': 'Select at least one grouped data!',
  'user.management.operation.table.sourceType.1': 'Automatically created',
  'user.management.operation.table.sourceType.2': 'Manual created',
  'user.management.operation.table.sourceType.3': 'Marketing introduction',
  'user.management.operation.table.btn.1': 'Batch add tags',
  'user.management.operation.table.btn.2': 'Batch delete tags',
  'user.management.operation.modal.tags.placeholder':
    'Please enter customer tags, press Enter to add more, you can enter multiple',
  'customerInformation.create.customer.email.tips':
    'The length of recipient, Cc, and Bcc delivery should not exceed 2000 characters!',
  'customerInformation.create.customer.email.content.tips':
    'The email content cannot be empty!',
  'customerInformation.add.basicInformation.tags.tips.new':
    'Please select or create at least one tag!',
};
