export default {
  'allocation.please.select.ticket.tag': 'Select ticket tag',
  'workerOffers.copy.message': 'Copy',
  'workerOffers.references.message': 'Quote',
  'workerOffers.withdraw.message': 'Recall',
  'workerOffers.withdraw.message.tips': 'You recalled a message',
  'workerOffers.withdraw.message.edit': 'Edit',
  'workerOffers.agent.status.limit':
    'Online agents are at capacity, please try again later',
  'workerOffers.chatList.setting.translation.tips':
    'When enabled, your chats with customers will be translated in real time',
  'channel.allocation.detail.tiktok.title': 'Configure TikTok channel',
  'add.tiktok.channel.configuration.title': 'Add TikTok Shop channel',
  'add.tiktok.channel.configuration.title.update': 'Edit TikTok Shop channel',
  'add.tiktok.channel.configuration.tips': 'Please add a TikTok Shop channel',
  'add.tiktok.channel.configuration.tips.update':
    'Please edit the TikTok Shop channel',
  'tiktokRegion.channel.configuration.title.1':
    'Select your TikTok Shop seller region',
  'tiktokRegion.channel.configuration.title.tips.1':
    'If you have more than one TikTok Shop region, select the one you want to set up first. You can add the others later',
  'tiktokRegion.channel.configuration.title.2': 'Authorize to get the token',
  'tiktokRegion.channel.configuration.title.tips.2':
    'To receive your TikTok messages, you need to grant access to ConnectNow',
  'workerOffers.chatList.setting.customer.avatar.color':
    'Customer avatar color settings',
  'workerOffers.chatList.setting.customer.avatar.color.table.order':
    'Matching order',
  'workerOffers.chatList.setting.customer.avatar.color.table.customer.color':
    'Customer avatar color',
  'workerOffers.chatList.setting.customer.avatar.color.table.add':
    'Create rule',
  'workerOffers.chatList.setting.customer.avatar.color.table.other.tag':
    'Other tags',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.add':
    'Create rule',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.edit':
    'Edit rule',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.title':
    'Tag name:',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.color.placeholder':
    'Select a color',
  'workerOffers.chatLayout.userNav.beInputting': 'Typing...',
  'channel.allocation.detail.tiktok.title': 'Configure TikTok channel',
  'add.tiktok.channel.configuration.title': 'Add TikTok Shop channel',
  'add.tiktok.channel.configuration.title.update': 'Edit TikTok Shop channel',
  'add.tiktok.channel.configuration.tips': 'Please add a TikTok Shop channel',
  'add.tiktok.channel.configuration.tips.update':
    'Please edit the TikTok Shop channel',
  'tiktokRegion.channel.configuration.title.1':
    'Select your TikTok Shop seller region',
  'tiktokRegion.channel.configuration.title.tips.1':
    'If you have more than one TikTok Shop region, select the one you want to set up first. You can add the others later',
  'tiktokRegion.channel.configuration.title.2': 'Authorize to get the token',
  'tiktokRegion.channel.configuration.title.tips.2':
    'To receive your TikTok messages, you need to grant access to ConnectNow',
  'tiktokRegion.channel.configuration.title.3.modal.title.tips':
    "If this step isn't completed, we can't deliver your TikTok messages. Please confirm you've followed all instructions above before continuing",
  'user.management.operation.add.avator': 'Add personalized avatar',
  'user.management.operation.add.avator.nickname': 'Your nickname',
  'user.management.operation.add.avator.avatar': 'Your avatar',
  'user.management.operation.add.avator.setting': 'Personalization settings',
  'user.management.operation.add.avator.nickname.placeholder':
    'Enter your nickname',
  'user.management.operation.add.avator.avatar.placeholder':
    'Upload your avatar',
  'user.management.operation.add.avator.setting.btn':
    'Personalized avatar settings',
  'user.management.operation.add.avator.setting.delete.confirm':
    'Are you sure you want to delete this personalized avatar setting?',
  'user.management.operation.add.avator.setting.duplicate.tips':
    'This channel is already used by another avatar rule!',
  'personal.tabs.3': 'Personalized avatar',
  'personal.tabs.3.tips':
    'Normally, you only need to set your avatar on your profile page. To use different avatars and nicknames for different channels, you can set them here',
  'tiktokRegion.channel.configuration.title.2.shop':
    'Authorize your account and select a TikTok Shop to connect',
  'tiktokRegion.channel.configuration.title.tips.2.shop':
    "To connect to TikTok's messaging and order system, please authorize ConnectNow to access your TikTok Shop information. If you have multiple shops, please select one",
  'tiktokRegion.channel.configuration.title.3.modal.title.tips':
    "If this step isn't completed, we can't deliver your TikTok messages. Please confirm you've followed all instructions above before continuing",
  'channel.allocation.detail.select.account.tiktok.name': 'Shop name:',
  'channel.allocation.detail.select.account.tiktok.name.placeholder':
    'Enter shop name, press Enter to search',
  'channel.allocation.detail.select.account.tiktok.shopId': 'Shop ID:',
  'channel.allocation.detail.select.account.tiktok.shopName': 'Shop name:',
  'channel.allocation.detail.select.account.tiktok.shopCode': 'Shop code:',
  'workerOffers.withdraw.message.tips.1': 'You recalled a message',
  'ticketOrCustomerSettings.config.title.1': 'Ticket type definition',
  'ticketOrCustomerSettings.config.title.2': 'Ticket field definition',
  'ticketOrCustomerSettings.config.title.3': 'Customer field definition',
  'ticketOrCustomerSettings.config.title.4': 'Permission settings',
  'agentOperationSettings.config.title.1': 'SLA settings',
  'agentOperationSettings.config.title.2': 'Business hours',
  'agentOperationSettings.config.title.3': 'Agent status definition',
};
