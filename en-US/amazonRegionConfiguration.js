export default {
  'add.amazonRegion.channel.configuration.title': 'Add Amazon',
  'add.amazonRegion.channel.configuration.title.update': 'Update Amazon',
  'add.amazonRegion.channel.configuration.tips': 'Please add Amazon',
  'amazonRegion.channel.configuration.title.1':
    'Choose your Amazon seller region',
  'amazonRegion.channel.configuration.title.tips.1':
    "If you have more than one Amazon region, that's okay. Simply select the region you want to set up first, and we can add other regions later.",
  'amazonRegion.channel.configuration.step.1.region': 'North America',
  'amazonRegion.channel.configuration.step.2.region': 'European region',
  'amazonRegion.channel.configuration.step.3.region': 'Far East region',
  'amazonRegion.channel.configuration.1.finish': 'Selection completed',
  'amazonRegion.channel.configuration.title.2': 'Perform authorization',
  'amazonRegion.channel.configuration.title.2.btn': 'Go to authorization',
  'amazonRegion.channel.configuration.title.2.btn.finish':
    'Finish authorization',
  'amazonRegion.channel.configuration.title.tips.2':
    'In order for us to be able to retrieve your Amazon message, you need to grant ConnectNow access permission.',
  'amazonRegion.channel.configuration.title.3':
    'Go to Amazon to configure Message',
  'amazonRegion.channel.configuration.title.tips.3':
    'You can follow these steps to configure Message on the Amazon website.',
  'amazonRegion.channel.configuration.title.3.go': 'Go to configuration',
  'amazonRegion.channel.configuration.title.3.go.finish':
    'Completed configuration',
  'amazonRegion.channel.configuration.title.step.1.step':
    '1. Copy your ConnectNow email',
  'amazonRegion.channel.configuration.title.step.2.step':
    '2. Log in to your Amazon seller account and ensure the same Amazon marketplace region is displayed at the top of the screen.',
  'amazonRegion.channel.configuration.title.step.3.step':
    '3. Click {value1} in the upper right corner, go to {value2}, ensure {value3} is set to Chinese or English; scroll down to {value4} and click {value5}.',
  'amazonRegion.channel.configuration.title.step.3.step.1': '"Settings"',
  'amazonRegion.channel.configuration.title.step.3.step.2':
    '"Notification Preferences"',
  'amazonRegion.channel.configuration.title.step.3.step.3':
    '"Send notifications in this language"',
  'amazonRegion.channel.configuration.title.step.3.step.4': '"Messages"',
  'amazonRegion.channel.configuration.title.step.3.step.5': '"Edit"',
  'amazonRegion.channel.configuration.title.step.4.step': '4. Confirm ',
  'amazonRegion.channel.configuration.title.step.4.step.1': '"Buyer Messages"',
  'amazonRegion.channel.configuration.title.step.4.step.2': ' and ',
  'amazonRegion.channel.configuration.title.step.4.step.3':
    '"Delivery Failures" ',
  'amazonRegion.channel.configuration.title.step.4.step.4': 'are both checked.',
  'amazonRegion.channel.configuration.title.step.5.step': '5. Enter your ',
  'amazonRegion.channel.configuration.title.step.5.step.1': 'ConnectNow email ',
  'amazonRegion.channel.configuration.title.step.5.step.2':
    'in the two fields.',
  'amazonRegion.channel.configuration.title.step.6.step':
    '6. If any other email addresses are listed under "Buyer Messages" or "Delivery Failures," remove any that forward to your helpdesk.',
  'amazonRegion.channel.configuration.title.step.7.step': '7. Click',
  'amazonRegion.channel.configuration.title.step.7.step.1': ' "Save" ',
  'amazonRegion.channel.configuration.title.step.7.step.2':
    'if you made any changes.',
  'amazonRegion.channel.configuration.title.step.8.step':
    '8. Repeat steps 1 through 8 for each Amazon seller region.',
  'amazonRegion.channel.configuration.title.3.modal.title':
    "Are you sure you're done with setup?",
  'amazonRegion.channel.configuration.title.3.modal.title.tips':
    "If this step is not completed, we won't be able to deliver your Amazon messages and data. Please confirm that you have followed all the instructions on this page before continuing",
  'amazonRegion.channel.configuration.title.4':
    'Intelligent Customer Service Settings',
  'amazonRegion.channel.configuration.title.tips.4':
    'You can configure the relevant information of the intelligent customer service here.',
  'amazonRegion.channel.configuration.title.5': 'Adding channel information',
  'amazonRegion.channel.configuration.title.tips.5':
    'Please enter channel information',
};
