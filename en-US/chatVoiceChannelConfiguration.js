export default {
  'add.chatVoice.channel.configuration.title':
    'Add Web Live Voice Configuration',
  'add.chatVoice.channel.configuration.title.update':
    'Update Web Live Voice Configuration',
  'app.add.chatVoice.channel.configuration.title':
    'Add APP Live Voice Configuration',
  'app.add.chatVoice.channel.configuration.title.update':
    'Update APP Live Voice Configuration',
  'web.video.channel.configuration.title.update':
    'Update Web Live Video Configuration',
  'web.video.channel.configuration.title': 'Add Web Live Video Configuration',
  'app.video.channel.configuration.title.update':
    'Update App Live Video Configuration',
  'app.video.channel.configuration.title': 'Add App Live Video Configuration',
  'app.video.channel.configuration.live.templete':
    'Do you want to end this chat and start a video call?',
  'add.chatVoice.channel.configuration.tips':
    'Simplify customer interactions via web live voice, enabling faster and more effective responses to their inquiries.',
  'app.add.chatVoice.channel.configuration.title':
    'Add live chat channel on APP',
  'app.add.chatVoice.channel.configuration.tips':
    'Simplify customer interactions via app live voice, enabling faster and more effective responses to their inquiries.',
  'add.chatVideo.channel.configuration.tips':
    'Simplify customer interactions via web live video, enabling faster and more effective responses to their inquiries.',
  'app.add.chatVideo.channel.configuration.tips':
    'Simplify customer interactions via app live video, enabling faster and more effective responses to their inquiries.',
  'chatVoice.channel.configuration.title.1': 'Basic information',
  'chatVoice.channel.configuration.title.tips.1':
    'Please fill in the channel basic information',
  'chatVoice.channel.configuration.title.2': 'Appearance settings',
  'chatVoice.channel.configuration.title.tips.2':
    'Here you can customize the chatbox, choose your favorite LOGO and color scheme',
  'chatVoice.channel.configuration.title.3': 'Basic feature settings',
  'chatVoice.channel.configuration.title.tips.3':
    'Here you can configure the basic functions of the chatbox',
  'chatVoice.channel.configuration.chat3.whatsApp.message':
    'After enabling, an WhatsApp icon will be displayed at the bottom of the chat window.',
  'chatVoice.channel.configuration.chat3.whatsApp': 'Display WhatsApp channel',
  'chatVoice.channel.configuration.chat3.email.message':
    'After enabling, an email icon will be displayed at the bottom of the chat window.',
  'chatVoice.channel.configuration.chat3.email': 'Display email channel',
  'chatVoice.channel.configuration.chat3.email.select':
    'Associated email channels',
  'chatVoice.channel.configuration.chat3.email.select.placeholder':
    'Please select the associated email channel',
  'chatVoice.channel.configuration.chat3.email.select.message':
    'After associating the email, users can directly click the email icon at the bottom of the chatbox for contact.',
  'chatVoice.channel.configuration.chat3.WhatsApp.select':
    'Associated WhatsApp channels',
  'chatVoice.channel.configuration.chat3.WhatsApp.select.placeholder':
    'Please select the associated WhatsApp channel',
  'chatVoice.channel.configuration.chat3.whatsApp.select.message':
    'After associating the WhatsApp number, users can click directly on the WhatsApp icon below the chatbox to initiate a WhatsApp conversation',
  'chatVoice.channel.configuration.title.4':
    'Intelligent customer service settings',
  'chatVoice.channel.configuration.title.tips.4':
    'Here you can configure the information related to the intelligent customer service',
  'chatVoice.channel.configuration.title.5': 'Deploy',
  'chatVoice.channel.configuration.title.tips.5':
    'Please add a chatbox to your website according to the next description',
  'chatVoice.channel.configuration.channel.name': 'Channel name',
  'chatVoice.channel.configuration.channel.name.placeholder':
    'Enter the channel name',
  'chatVoice.channel.configuration.chatVoice.types': 'Language',
  'chatVoice.channel.configuration.chatVoice.types.placeholder':
    'Please select language',
  'chatVoice.channel.configuration.chat2.logo': 'Company Logo',
  'chatVoice.channel.configuration.chat2.logo.message1':
    'Only JPG or PNG are supported, and the image size must not exceed 500KB.',
  'chatVoice.channel.configuration.chat2.logo.message2':
    'The icon will be displayed in the upper left corner of the chatbox, it is recommended to upload a 50*20px PNG image',
  'chatVoice.channel.configuration.chat2.chatBoxName.placeholder':
    'Enter chatbot name',
  'chatVoice.channel.configuration.chat2.chatBoxName': 'Chatbot name',
  'chatVoice.channel.configuration.chat2.chatBoxName.message':
    'It will display in the upper left corner of the chatbox',
  'chatVoice.channel.configuration.chat2.templete': 'Theme color',
  'chatVoice.channel.configuration.chat2.templete.custom': 'Custom',
  'chatVoice.channel.configuration.chat2.templete.color': 'Current color：',
  'chatVoice.channel.configuration.chat2.templete.placeholder':
    'Please select color',
  'chatVoice.channel.configuration.chat2.boxColor': 'Agent chatbox color',
  'chatVoice.channel.configuration.chat2.userBox': 'User chatbox color',
  'chatVoice.channel.configuration.chat2.information.configuration.completed':
    'You have completed the setting of the chatbox appearance, you can click the preview button to preview the style.',
  'chatVoice.channel.configuration.work.panels.checkbox': 'Enable',
  'chatVoice.channel.configuration.chat3.form': 'User information form page',
  'chatVoice.channel.configuration.chat3.form.message':
    'Suggest to open, after opening users need to input basic information first, then can conduct subsequent communication',
  'chatVoice.channel.configuration.chat3.welcome': 'Initial greeting',
  'chatVoice.channel.configuration.chat3.welcome.words': 'A brief greeting',
  'chatVoice.channel.configuration.chat3.welcome.words.placeholder':
    'Please enter welcome message',
  'chatVoice.channel.configuration.chat3.interval.placeholder':
    'Please enter the automatic invitation session time',
  'chatVoice.channel.configuration.chat3.welcome.words.message':
    'Here settings please correspond to the language you selected in the first step',
  'chatVoice.channel.configuration.chat3.welcome.QA': 'Trigger FAQ',
  'chatVoice.channel.configuration.chat3.welcome.QA.placeholder':
    'Please select the corresponding FAQ.',
  'chatVoice.channel.configuration.chat3.welcome.QA.message':
    'FAQ allows you to reply multiple answers to users at once, not only supporting text, but also supporting pictures and videos. If you select a certain FAQ, when users contact you, the system will automatically reply with the standard answer configured in the FAQ. If you have not set up any FAQ, you can click',
  'chatVoice.channel.configuration.chat3.welcome.QA.message.1': ' here ',
  'chatVoice.channel.configuration.chat3.welcome.QA.message.2': 'to set it up.',
  'chatVoice.channel.configuration.chat3.talk': 'Auto popup chat',
  'chatVoice.channel.configuration.chat3.talk.ge': 'Interval',
  'chatVoice.channel.configuration.chat3.talk.ge2':
    'seconds, the system will automatically pop up the chatbox',
  'chatVoice.channel.configuration.chat3.message':
    'When customers browse your website, the system will automatically trigger a chat window to actively invite customers for consultation.',
  'chatVoice.channel.configuration.chat3.voice.message':
    'Customers can click on the video button below the chat window to engage in voice communication with the agent.',
  'chatVoice.channel.configuration.chat3.voice': 'Online voice communication',
  'chatVoice.channel.configuration.chat3.video.message':
    'Customers can click on the video button below the chat window to engage in video communication with the agent.',
  'chatVoice.channel.configuration.chat3.video': 'Online video communication',
  'chatVoice.channel.configuration.chat3.evaluate.message':
    'After ending the chat, the system automatically pops up a satisfaction evaluation for the agent.',
  'chatVoice.channel.configuration.chat3.evaluate': 'Satisfaction evaluation',
  'chatVoice.channel.configuration.chat3.information.configuration.completed':
    'You have completed the basic functionality settings, these will be implemented in the chatbox after you save',
  'chatVoice.channel.configuration.chat4.mode.message': `Choose "Intelligent customer service", there will be a robotic response to customer queries first, if the chatbot cannot answer, users can switch to human agent service at any time.`,
  'chatVoice.channel.configuration.chat4.mode.message.1': ` Choose "Only agent", only human agents will answer customer questions.`,
  'chatVoice.channel.configuration.chat4.mode.message.2': `Choose "Only chatbot", only robotic customer service will answer customer questions.`,
  'chatVoice.channel.configuration.chat4.mode.1':
    'Intelligent customer service',
  'chatVoice.channel.configuration.chat4.mode.2': 'Only agent',
  'chatVoice.channel.configuration.chat4.mode.3': 'Only chatbot',
  'chatVoice.channel.configuration.chat4.mode': 'Customer service mode',
  'chatVoice.channel.configuration.chat4.robot.message':
    "The chatbot name will be displayed above the chatbot's answer",
  'chatVoice.channel.configuration.chat4.robot.placeholder': 'Enter bot name',
  'chatVoice.channel.configuration.chat4.robot': 'Chatbot name',
  'chatVoice.channel.configuration.chat4.language.message': `With this feature turned on, the system will automatically identify the language of the user's input question; if not turned on, it will use the language of the user's browser by default.`,
  'chatVoice.channel.configuration.chat4.language':
    'Automatic language identification',
  'chatVoice.channel.configuration.chat4.document': 'Document knowledge base',
  'chatVoice.channel.configuration.chat4.document.placeholder':
    'Please select a document knowledge base',
  'chatVoice.channel.configuration.chat4.document.message.1':
    'This document knowledge base only displays external knowledge bases, please configure the knowledge base on the ',
  'chatVoice.channel.configuration.chat4.document.message':
    ' Document knowledge base',
  'chatVoice.channel.configuration.chat4.document.message.2': 'page.',
  'chatVoice.channel.configuration.chat4.ai.message':
    'You can configure whether to enable Generative AI.',
  'chatVoice.channel.configuration.chat4.ai': 'Integrating Generative AI',
  'chatVoice.channel.configuration.chat4.workers': `Show "Transfer to agent" button for unknown questions`,
  'chatVoice.channel.configuration.chat4.workers.message': `If enabled, when the chatbot does not know the answer, it will automatically display a "Transfer to agent" button below the chatbot's response content`,
  'chatVoice.channel.configuration.chat4.unknown':
    "Chatbot's response when encountering unknown questions",
  'chatVoice.channel.configuration.chat4.unknown.placeholder':
    "Please enter the chatbot's reply to unknown questions",
  'chatVoice.channel.configuration.chat4.unknown.message':
    "This setting is the chatbot's response when encountering unknown questions",
  'chatVoice.channel.configuration.chat4.information.configuration.completed':
    'You have completed the setup of the intelligent customer service, these will be implemented in the chatbox after you save.',
  'chatVoice.channel.configuration.chat5.message':
    'Copy the following code and insert it within the <body> </body> tag on your website.',
  'chatVoice.channel.configuration.chat5.message.link':
    'Chat link: Copy the following link into your website code',
  'live.chatVoice.title': 'Chatbox preview area',
  'live.chatVoice.title.subtitle': 'Here you can preview the chatbox effect',
  'live.chatVoice.customer': 'Customer',
  'live.chatVoice.customer.Dialogue':
    'Can you let me know what are the main features of the product?',
  'live.chatVoice.submit': "Let's chat",
  'live.chatVoice.end': 'End of Conversation',
  'live.chatVoice.video': 'Video Call',
  'chatVoice.channel.configuration.cancel.btn': 'Cancel',
  'chatVoice.channel.configuration.next.btn': 'Next step',
  'chatVoice.channel.configuration.complete.btn': 'Finished',
  'chatVoice.channel.configuration.title.knowledge_unknown_reply':
    'With my current skills, I am unable to answer the question you posed. If needed, you can directly choose our human agent for more professional support😊',
  'chatVoice.channel.configuration.chat5.end': `Please note: After integrating the above code into your website, please contact the "ConnectNow" administrator to add the specified domains to the whitelist. The chat component will only display correctly once the whitelist configuration is complete.`,
  'chatVoice.channel.configuration.chat5.end.1': ` `,
  'chatVoice.channel.configuration.channel.name.web': 'Website domain',
  'chatVoice.channel.configuration.channel.name.placeholder.web':
    'Please enter website domain name',
  'chatVoice.channel.configuration.chat4.workers.content':
    "I'm sorry, I cannot answer this question for you, please contact customer support.",
  'chatVoice.channel.configuration.chat4.workers.position': 'Location',
  'chatVoice.channel.configuration.chat4.workers.zhuan': 'Transfer to Agent',
  'live.chatVoice.customer.Dialogue.product':
    'Which product would you like to know?',
  'chatVoice.channel.configuration.chat4.workers.position.zhuan':
    'Transfer to Agent',
  'chatVoice.channel.configuration.chat5.message.Settings':
    'Deployment settings',
  'chatVoice.channel.configuration.channel.name.placeholder.error':
    'Can only enter Chinese characters, uppercase and lowercase letters, numbers, "-" and "_"',
  'chatVoice.channel.configuration.channel.chatBoxName.placeholder.error':
    'Only Chinese characters, uppercase and lowercase letters, spaces are allowed',
  'chatVoice.channel.configuration.chat1.document.placeholder.language':
    'Trigger FAQ data change Please select again',
  'chatVoice.channel.configuration.channel.website':
    'The website domain name format is as follows: www.connectnow.cn',
  'chatVoice.channel.configuration.channel.website.name.placeholder.error':
    'Please enter the website domain of the rules',
  'chatVoice.channel.configuration.work.panels.checkbox.ccp':
    'Whether to enable language recognition',
  'chatVoice.channel.configuration.chat3.talk.Input': 'Auto popup message',
  'chatVoice.channel.configuration.chat3.talk.Input.placeholder':
    'Please enter the auto-invite conversation welcome message',
  'chatVoice.channel.configuration.chat3.talk.Input.message':
    'Set the welcome message displayed when automatic invitation for chat pops up here.',
  'chatVoice.channel.configuration.title.pop_welcome_msg':
    'Hello, I am the ConnectNow intelligent customer service, is there anything I can help you with?',
  'chatVoice.channel.configuration.chat4.workers.keyword.message':
    'When customers enter this keyword, the system will automatically transfer to a human customer service agent.',
  'chatVoice.channel.configuration.chat4.workers.keyword':
    'Transfer to Agent Keyword',
  'chatVoice.channel.configuration.chat4.document.placeholder.keyword':
    'Please input at least one keyword',
};
