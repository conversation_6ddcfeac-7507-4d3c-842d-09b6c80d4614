export default {
  'feedbackPerformance.title': 'AIGC Chatbot Feedback Performance',
  'feedbackPerformance.card.3': 'Top 5 most liked questions',
  'feedbackPerformance.card.4': 'Top 5 most disliked questions',
  'feedbackPerformance.card.1.tabs.1': 'Like rate',
  'feedbackPerformance.card.1.tabs.2': 'Dislike rate',
  'feedbackPerformance.card.1.tabs.3': 'Engagement rate',
  'feedbackPerformance.card.2.text':
    'During this period, the question "{question}" received the highest number of dislikes, totaling {number}. Please pay attention to whether it is necessary to update and maintain the knowledge.',
  'feedbackPerformance.card.2.text.up': 'Increase',
  'feedbackPerformance.card.2.text.down': 'Decline',
  'feedbackPerformance.card.5.text.up': 'Like',
  'feedbackPerformance.card.5.text.down': 'Dislike',
  'feedbackPerformance.card.5.select': 'Select the channel:',
  'feedbackPerformance.card.5.select.placeholder':
    'Please select your channels (multiple channels will be automatically combined).',
  'feedbackPerformance.card.5': 'Like/Dislike interaction trend chart',
  'feedbackPerformance.card.6':
    'Number of likes/dislikes interactions across different channels',
  'feedbackPerformance.card.1.tips.1':
    'Calculate the proportion of questions that received likes among all the questions answered by the intelligent customer service during a specified time period. Like rate = sum(number of likes) / sum(number of questions)',
  'feedbackPerformance.card.1.tips.2':
    'Calculate the proportion of dislikes questions among all questions answered by the intelligent customer service within a specified period of time. Dislike rate = sum(number of dislikes) / sum(number of questions).',
  'feedbackPerformance.card.1.tips.3':
    'Count the total number of questions answered by the intelligent customer service and the number of questions that were liked or disliked within a specified time period. Interaction rate = sum(number of likes + number of dislikes) / sum(number of questions).',
  'feedbackPerformance.card.2.tips':
    'Based on the questions answered by the intelligent customer service, identify and summarize key data, including the questions with the highest number of dislikes and their quantities, within the specified time period.',
  'feedbackPerformance.card.3.tips':
    'Count the top 5 questions with the most likes for the answers given by the intelligent customer service during the specified time period.',
  'feedbackPerformance.card.4.tips':
    'Count the top 5 questions with the most dislikes for the answers provided by the AI chatbot during the specified time period.',
  'feedbackPerformance.card.5.tips':
    'Display the trend of the number of likes and dislikes at different time points, multiple channels can be specified, and all channels are counted by default.',
  'feedbackPerformance.card.6.tips':
    'Display the number of questions that received likes and dislikes from different channels within different time periods',
};
