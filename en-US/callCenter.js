export default {
  'product.callCenter.title.first': 'Intelligent Call Center',
  'product.callCenter.title.second': ' With Global Coverage',
  'product.callCenter.subtitle':
    'Based on global line coverage, powered by GenAI to enable automated and intelligent workflows, offering a more efficient, convenient, and personalized customer interaction experience.',
  'product.callCenter.content.title':
    'Global Line Coverage in 80+ Countries and Regions',
  'product.callCenter.content.desc':
    'No matter where your business operates globally, you can provide a smooth and seamless calling experience for local customers.',
  'product.callCenter.card1.title':
    'Assigning Customer Inquiries to the Most Suitable Agent',
  'product.callCenter.card1.desc':
    'The intelligent routing mechanism intelligently assigns incoming calls to the most suitable agent based on customer needs, issue type, and agent expertise.',
  'product.callCenter.card2.title': 'Flexible Call Collaboration Mechanism',
  'product.callCenter.card2.desc':
    "If an agent is unable to independently resolve a customer's issue, the AI call center supports seamless transfer to another agent with the relevant expertise, ensuring fast and effective resolution. Additionally, the system supports multi-party voice conferencing, allowing agents to invite colleagues or supervisors to join the call and collaboratively resolve complex issues.",
  'product.callCenter.card3.title': 'Intelligent Call Recording and Analysis',
  'product.callCenter.card3.desc':
    'Each call is automatically recorded, and AI converts the voice into text to generate key content summaries. This makes the service content traceable for future reference and analysis. Whether for training, quality control, or compliance purposes, the recordings and transcripts help businesses better track service quality and serve as valuable internal knowledge assets.',
  'product.callCenter.card4.title': 'Automated Ticket Records',
  'product.callCenter.card4.desc':
    'All phone communications with customers are automatically recorded as tickets for easy querying, follow-ups, reassignment, and callbacks. These tickets include customer basic information, as well as the call time, duration, key content during the conversation, and related customer requests.',
};
