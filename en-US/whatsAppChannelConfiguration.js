export default {
  'add.whatsApp.channel.configuration.title': 'Adding WhatsApp Channel',
  'add.whatsApp.channel.configuration.title.update': 'Update WhatsApp Channel',
  'add.whatsApp.channel.configuration.tips':
    'Let customers more easily contact enterprises through WhatsApp social messages, faster respond to customer demands',
  'whatsApp.channel.configuration.title.1': 'Binding WhatsApp Business Account',
  'whatsApp.channel.configuration.title.tips.1':
    'Let customers more easily contact enterprises through WhatsApp social messaging, respond to customer needs faster',
  'whatsApp.channel.configuration.title.2': 'Selecting a WhatsApp phone number',
  'whatsApp.channel.configuration.title.tips.2':
    'Please select the phone number to display when subsequently contacting customers',
  'whatsApp.channel.configuration.title.3': 'Adding channel information',
  'whatsApp.channel.configuration.title.tips.3':
    'Please enter channel information',
  'whatsApp.channel.configuration.title.4':
    'Intelligent customer service settings',
  'whatsApp.channel.configuration.title.tips.4':
    'Here you can configure the information related to the intelligent customer service',
  'whatsApp.channel.configuration.title.5': 'Deploy',
  'whatsApp.channel.configuration.title.tips.5':
    'Please add a chatbox to your website according to the next description',
  'whatsApp.channel.configuration.channel.name': 'Channel name',
  'whatsApp.channel.configuration.channel.name.placeholder':
    'Enter the channel name',
  'whatsApp.channel.configuration.whatsApp.types': 'Language',
  'whatsApp.channel.configuration.whatsApp.types.placeholder':
    'Please select language',
  'whatsApp.channel.configuration.chat2.logo': 'Company Logo',
  'whatsApp.channel.configuration.chat2.logo.message1':
    'Only JPG or PNG are supported, and the image size must not exceed 500KB.',
  'whatsApp.channel.configuration.chat2.logo.message2':
    'The icon will be displayed in the upper left corner of the chatbox, it is recommended to upload a 50*20px PNG image',
  'whatsApp.channel.configuration.chat2.chatBoxName.placeholder':
    'Enter chatbot name',
  'whatsApp.channel.configuration.chat2.chatBoxName': 'Chatbot name',
  'whatsApp.channel.configuration.chat2.chatBoxName.message':
    'It will display in the upper left corner of the chatbox',
  'whatsApp.channel.configuration.chat2.templete': 'Theme color',
  'whatsApp.channel.configuration.chat2.templete.custom': 'Custom',
  'whatsApp.channel.configuration.chat2.templete.color': 'Current color：',
  'whatsApp.channel.configuration.chat2.templete.placeholder':
    'Please select color',
  'whatsApp.channel.configuration.chat2.boxColor': 'Agent chatbox color',
  'whatsApp.channel.configuration.chat2.userBox': 'User chatbox color',
  'whatsApp.channel.configuration.work.panels.checkbox': 'Enable',
  'whatsApp.channel.configuration.chat3.form.message':
    'Suggest to open, after opening users need to input basic information first, then can conduct subsequent communication',
  'whatsApp.channel.configuration.chat3.welcome': 'Initial greeting',
  'whatsApp.channel.configuration.chat3.welcome.words': 'A brief greeting',
  'whatsApp.channel.configuration.chat3.welcome.words.placeholder':
    'Please enter welcome message',
  'whatsApp.channel.configuration.chat3.interval.placeholder':
    'Please enter the automatic invitation session time',
  'whatsApp.channel.configuration.chat3.welcome.words.message':
    'Here settings please correspond to the language you selected in the first step',
  'whatsApp.channel.configuration.chat3.welcome.QA': 'Trigger FAQ',
  'whatsApp.channel.configuration.chat3.welcome.QA.placeholder':
    'Please select the corresponding FAQ.',
  'whatsApp.channel.configuration.chat3.welcome.QA.message':
    'FAQ allows you to reply multiple answers to users at once, not only supporting text, but also supporting pictures and videos. If you select a certain FAQ, when users contact you, the system will automatically reply with the standard answer configured in the FAQ. If you have not set up any FAQ, you can click',
  'whatsApp.channel.configuration.chat3.welcome.QA.message.1': ' here ',
  'whatsApp.channel.configuration.chat3.welcome.QA.message.2': 'to set it up.',
  'whatsApp.channel.configuration.chat3.talk': 'Auto popup chat',
  'whatsApp.channel.configuration.chat3.talk.ge': 'Interval',
  'whatsApp.channel.configuration.chat3.talk.ge2':
    'seconds, the system will automatically pop up the chatbox',
  'whatsApp.channel.configuration.chat3.message':
    'When customers browse your website, the system will automatically trigger a chat window to actively invite customers for consultation.',
  'whatsApp.channel.configuration.chat3.voice.message':
    'Customers can click on the video button below the chat window to engage in voice communication with the agent.',
  'whatsApp.channel.configuration.chat3.voice': 'Online voice communication',
  'whatsApp.channel.configuration.chat3.video.message':
    'Customers can click on the video button below the chat window to engage in video communication with the agent.',
  'whatsApp.channel.configuration.chat3.video': 'Online video communication',
  'whatsApp.channel.configuration.chat3.evaluate.message':
    'After ending the chat, the system automatically pops up a satisfaction evaluation for the agent.',
  'whatsApp.channel.configuration.chat3.evaluate': 'Satisfaction evaluation',
  'whatsApp.channel.configuration.chat3.information.configuration.completed':
    'You have completed the basic functionality settings, these will be implemented in the chatbox after you save',
  'whatsApp.channel.configuration.chat4.mode.message': `Choose "Intelligent customer service", there will be a robotic response to customer queries first, if the chatbot cannot answer, users can switch to human agent service at any time.`,
  'whatsApp.channel.configuration.chat4.mode.message.1': ` Choose "Only agent", only human agents will answer customer questions.`,
  'whatsApp.channel.configuration.chat4.mode.message.2': `Choose "Only chatbot", only robotic customer service will answer customer questions.`,
  'whatsApp.channel.configuration.chat4.mode.1': 'Intelligent customer service',
  'whatsApp.channel.configuration.chat4.mode.2': 'Only agent',
  'whatsApp.channel.configuration.chat4.mode.3': 'Only chatbot',
  'whatsApp.channel.configuration.chat4.mode': 'Customer service mode',
  'whatsApp.channel.configuration.chat4.robot.message':
    "The chatbot name will be displayed above the chatbot's answer",
  'whatsApp.channel.configuration.chat4.robot.placeholder': 'Enter bot name',
  'whatsApp.channel.configuration.chat4.robot': 'Chatbot name',
  'whatsApp.channel.configuration.chat4.language.message': `With this feature turned on, the system will automatically identify the language of the user's input question; if not turned on, it will use the language of the user's browser by default.`,
  'whatsApp.channel.configuration.chat4.language':
    'Automatic language identification',
  'whatsApp.channel.configuration.chat4.document': 'Document knowledge base',
  'whatsApp.channel.configuration.chat4.document.placeholder':
    'Please select a document knowledge base',
  'whatsApp.channel.configuration.chat4.document.message.1':
    'This document knowledge base only displays external knowledge bases, please configure the knowledge base on the ',
  'whatsApp.channel.configuration.chat4.document.message':
    ' Document knowledge base',
  'whatsApp.channel.configuration.chat4.document.message.2':
    'Configure knowledge base on the page',
  'whatsApp.channel.configuration.chat4.ai.message':
    'You can configure whether to enable Generative AI.',
  'whatsApp.channel.configuration.chat4.ai': 'Integrating Generative AI',
  'whatsApp.channel.configuration.chat4.workers': `Show "Transfer to agent" button for unknown questions`,
  'whatsApp.channel.configuration.chat4.workers.message': `If enabled, when the chatbot does not know the answer, it will automatically display a "Transfer to agent" button below the chatbot's response content`,
  'whatsApp.channel.configuration.chat4.unknown':
    "Chatbot's response when encountering unknown questions",
  'whatsApp.channel.configuration.chat4.unknown.placeholder':
    "Please enter the chatbot's reply to unknown questions",
  'whatsApp.channel.configuration.chat4.unknown.message':
    "This setting is the chatbot's response when encountering unknown questions",
  'whatsApp.channel.configuration.chat5.message':
    'Copy the following code and insert it within the <body> </body> tag on your website.',
  'whatsApp.channel.configuration.chat5.message.link':
    'Chat link: Copy the following link into your website code',
  'live.whatsApp.title': 'Chatbox preview area',
  'live.whatsApp.title.subtitle': 'Here you can preview the chatbox effect',
  'live.whatsApp.customer': 'Customer',
  'live.whatsApp.customer.Dialogue':
    'Can you let me know what are the main features of the product?',
  'live.whatsApp.submit': "Let's chat",
  'live.whatsApp.end': 'End of Conversation',
  'live.whatsApp.video': 'Video Call',
  'whatsApp.channel.configuration.cancel.btn': 'Cancel',
  'whatsApp.channel.configuration.next.btn': 'Next step',
  'whatsApp.channel.configuration.complete.btn': 'Save',
  'whatsApp.channel.configuration.title.knowledge_unknown_reply':
    'With my current skills, I am unable to answer the question you posed. If needed, you can directly choose our human agent for more professional support😊',
  'whatsApp.channel.configuration.chat5.end': `Please note: After integrating the above code into your website, please contact the "ConnectNow" administrator to add the specified domains to the whitelist. The chat component will only display correctly once the whitelist configuration is complete.`,
  'whatsApp.channel.configuration.chat5.end.1': ` `,
  'whatsApp.channel.configuration.channel.name.web': 'Website domain',
  'whatsApp.channel.configuration.channel.name.placeholder.web':
    'Please enter website domain name',
  'whatsApp.channel.configuration.chat4.workers.content':
    "I'm sorry, I cannot answer this question for you, please contact customer support.",
  'whatsApp.channel.configuration.chat4.workers.position': 'Location',
  'whatsApp.channel.configuration.chat4.workers.zhuan': 'Transfer to Agent',
  'live.whatsApp.customer.Dialogue.product':
    'Which product would you like to know?',
  'whatsApp.channel.configuration.chat4.workers.position.zhuan':
    'Transfer to Agent',
  'whatsApp.channel.configuration.chat5.message.Settings':
    'Deployment settings',
  'whatsApp.channel.configuration.channel.name.placeholder.error':
    'Can only enter Chinese characters, uppercase and lowercase letters, numbers, "-" and "_"',
  'whatsApp.channel.configuration.channel.chatBoxName.placeholder.error':
    'Only Chinese characters, uppercase and lowercase letters, spaces are allowed',
  'whatsApp.channel.configuration.chat1.document.placeholder.language':
    'Trigger FAQ data change Please select again',
  'whatsApp.channel.configuration.channel.website':
    'The website domain name format is as follows: www.connectnow.cn',
  'whatsApp.channel.configuration.channel.website.name.placeholder.error':
    'Please enter the website domain of the rules',
  'whatsApp.channel.configuration.work.panels.checkbox.ccp':
    'Whether to enable language recognition',
  'whatsApp.channel.configuration.chat3.talk.Input': 'Auto popup message',
  'whatsApp.channel.configuration.chat3.talk.Input.placeholder':
    'Please enter the auto-invite conversation welcome message',
  'whatsApp.channel.configuration.chat3.talk.Input.message':
    'Set the welcome message displayed when automatic invitation for chat pops up here.',
  'whatsApp.channel.configuration.title.pop_welcome_msg':
    'Hello, I am the ConnectNow intelligent customer service, is there anything I can help you with?',
  'whatsApp.channel.configuration.chat4.workers.keyword.message':
    'When customers enter this keyword, the system will automatically transfer to a human customer service agent.',
  'whatsApp.channel.configuration.chat4.workers.keyword':
    'Transfer to Agent Keyword',
  'whatsApp.channel.configuration.chat4.document.placeholder.keyword':
    'Please input at least one keyword',
  'whatsApp.channel.configuration.title.2.please':
    'Please select WhatsApp call',
  'whatsApp.channel.configuration.chat1.information.configuration.completed':
    'You have successfully bound the WhatsApp Business account, please continue to select the WhatsApp phone number.',
  'whatsApp.channel.configuration.content1.btn': 'Binding WhatsApp account',
  'whatsApp.channel.configuration.chat2.information.configuration.completed':
    'You have successfully bound WhatsApp number:',
  'whatsApp.channel.configuration.chat4.information.configuration.completed':
    'You have completed the setup of the intelligent customer service,these will be implemented after you save.',
  'whatsApp.channel.configuration.chat3.form': 'Workflow Integration Panel',
  'whatsApp.channel.configuration.chat1.document.placeholder.whatsApp':
    'Please first bind WhatsApp account',
};
