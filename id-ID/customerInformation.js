export default {
    'customerInformation.customerName': '<PERSON><PERSON>ng:',
    'customerInformation.customerName.placeholder': '<PERSON><PERSON>an masukkan nama belakang dan tekan Enter untuk mencari',
    'customerInformation.emailAddress': 'Email:',
    'customerInformation.emailAddress.placeholder': '<PERSON><PERSON><PERSON> masukkan alamat email dan tekan Enter untuk mencari',
    'customerInformation.add.basicInformation.emailAddress.placeholder.1': '<PERSON>lakan masukkan alamat email',
    'customerInformation.customerBusinessName': '<PERSON>a bisnis pelanggan:',
    'customerInformation.customerBusinessName.placeholder': 'Silakan masukkan nama bisnis pelanggan dan tekan Enter untuk mencari',
    'customerInformation.sourceChannel': 'Saluran sumber:',
    'customerInformation.searchPlaceholder': 'Silakan masukkan informasi pelanggan dan tekan Enter untuk mencari',
    'customerInformation.customerInformation': ' Informasi pelanggan',
    'customerInformation.customerInformation1': 'Informasi pelanggan',
    'customerInformation.table.sureName': 'Nama Belakang',
    'customerInformation.table.customerName': 'Nama Depan',
    'customerInformation.table.customerCode': 'Nomor pelanggan',
    'customerInformation.table.mailAddress': 'Email',
    'customerInformation.table.telephonePrefixId': 'Kode area',
    'customerInformation.table.mobilePhoneNumber': 'Telepon',
    'customerInformation.table.customerLabel': 'Tag',
    'customerInformation.table.ContactTime': 'Waktu kontak terakhir',
    'customerInformation.table.ContactInformation': 'Informasi kontak',
    'customerInformation.table.nation': 'Negara',
    'customerInformation.table.creationMethod': 'Metode pembuatan',
    'customerInformation.table.groupName': 'Nama kelompok',
    'customerInformation.table.sourceChannel': 'Saluran sumber',
    'customerInformation.table.createTime': 'Waktu pembuatan',
    'customerInformation.table.address': 'Alamat surat',
    'customerInformation.table.operation': 'Tindakan',
    'customerInformation.table.contactCustomer': 'Hubungi pelanggan',
    'customerInformation.table.customer.detail': 'Detail',
    'customerInformation.table.editor': 'Perbarui',
    'customerInformation.add.basicInformation.customerID': 'ID anggota',
    'customerInformation.add.basicInformation.customerID.required': 'Silakan masukkan ID anggota',
    'customerInformation.add.basicInformation.addGroup': 'Tambah kelompok',
    'customerInformation.add.basicInformation.pattern': 'Format salah, hanya diperbolehkan huruf Cina, Inggris, angka, dan "."',
    'customerInformation.add.basicInformation.maxlength': 'Panjang tidak boleh melebihi 80 karakter',
    'customerInformation.add.basicInformation.maxlength2': 'Panjang tidak boleh melebihi 200 karakter',
    'customerInformation.add.basicInformation.maxlength3': 'Panjang tidak boleh melebihi 2000 karakter',
    'customerInformation.add.basicInformation.placeholder': 'Silakan masukkan',
    'customerInformation.add.basicInformation.title': 'Informasi dasar',
    'customerInformation.add.basicInformation.title2': 'Informasi media sosial pelanggan',
    'customerInformation.add.basicInformation.title3': 'Informasi tambahan pelanggan',
    'customerInformation.add.basicInformation.selectiveGrouping': 'Pilih kelompok',
    'customerInformation.add.basicInformation.selectiveGrouping.placeholder': 'Silakan pilih kelompok',
    'customerInformation.add.basicInformation.selectiveGrouping.required': 'Silakan pilih kelompok',
    'customerInformation.add.basicInformation.lastname': 'Nama belakang:',
    'customerInformation.add.basicInformation.lastname.placeholder': 'Silakan masukkan nama belakang',
    'customerInformation.add.basicInformation.lastname.required': 'Silakan masukkan nama belakang',
    'customerInformation.add.basicInformation.name': 'Nama depan:',
    'customerInformation.add.basicInformation.name.placeholder': 'Silakan masukkan nama depan',
    'customerInformation.add.basicInformation.name.required': 'Silakan masukkan nama depan',
    'customerInformation.add.basicInformation.name.pattern': 'Format nama salah, hanya diperbolehkan huruf Cina, Inggris, angka, spasi, tanda hubung, garis bawah dan "."',
    'customerInformation.add.basicInformation.name.maxlength': 'Panjang nama tidak boleh melebihi 200 karakter',
    'customerInformation.add.basicInformation.sourceChannel.placeholder': 'Silakan pilih saluran sumber',
    'customerInformation.add.basicInformation.channel.name': 'Silakan pilih nama saluran',
    'customerInformation.add.basicInformation.sourceChannel.required': 'Silakan pilih saluran sumber',
    'customerInformation.add.basicInformation.dateBirth': 'Tanggal lahir:',
    'customerInformation.add.basicInformation.sex': 'Jenis kelamin:',
    'customerInformation.add.basicInformation.sex.placeholder': 'Silakan pilih jenis kelamin',
    'customerInformation.add.basicInformation.contactNumber': 'Telepon:',
    'customerInformation.add.basicInformation.contactNumber.placeholder': 'Silakan masukkan telepon',
    'customerInformation.add.basicInformation.contactNumber.pattern': 'Format telepon salah, hanya diperbolehkan angka',
    'customerInformation.add.basicInformation.contactNumber.pattern1': 'Kata sandi harus mengandung setidaknya dua dari berikut: angka, Inggris, simbol; Rentang simbol ~!@#$%^&*()_+<>?.,',
    'customerInformation.add.basicInformation.contactNumber.maxlength': 'Panjang telepon tidak boleh melebihi 40 karakter',
    'customerInformation.add.basicInformation.emailAddress': 'Email:',
    'customerInformation.add.basicInformation.emailAddress.placeholder': 'Silakan masukkan alamat email',
    'customerInformation.add.basicInformation.Address.placeholder': 'Silakan pilih wilayah',
    'customerInformation.add.basicInformation.emailAddress.pattern': 'Format alamat email salah',
    'customerInformation.add.basicInformation.companyName': 'Nama perusahaan:',
    'customerInformation.add.basicInformation.companyName.placeholder': 'Silakan masukkan nama perusahaan',
    'customerInformation.add.basicInformation.position': 'Jabatan:',
    'customerInformation.add.basicInformation.position.placeholder': 'Silakan masukkan jabatan',
    'customerInformation.add.basicInformation.mailingAddress': 'Alamat surat',
    'customerInformation.add.basicInformation.mailingAddress.placeholder': 'Silakan masukkan alamat surat',
    'customerInformation.add.basicInformation.orderAddress': 'Alamat tagihan',
    'customerInformation.add.basicInformation.orderAddress.placeholder': 'Silakan masukkan alamat tagihan',
    'customerInformation.add.basicInformation.deliveryAddress': 'Alamat pengiriman',
    'customerInformation.add.basicInformation.deliveryAddress.placeholder': 'Silakan masukkan alamat pengiriman',
    'customerInformation.add.basicInformation.otherAddress': 'Alamat lainnya',
    'customerInformation.add.basicInformation.otherAddress.placeholder': 'Silakan masukkan alamat lainnya',
    'customerInformation.add.basicInformation.remark': 'Catatan',
    'customerInformation.add.basicInformation.remark.placeholder': 'Silakan masukkan catatan',
    'customerInformation.add.basicInformation.return.confirm': 'Mengklik kembali akan kehilangan data yang dimodifikasi saat ini, konfirmasi kembali?',
    'customerInformation.add.basicInformation.button.return': 'Kembali',
    'customerInformation.add.basicInformation.button.save': 'Simpan',
    'customerInformation.add.basicInformation.button.update': 'Perbarui',
    'customerInformation.modal.basicInformation.tips': 'Peringatan',
    'customerInformation.modal.basicInformation.button.ok': 'Lihat informasi pelanggan',
    'customerInformation.modal.basicInformation.button.cancel': 'Batal',
    'customerInformation.contactCustomer.info.title': 'Informasi pelanggan',
    'customerInformation.contactCustomer.info.customerName': 'Nama pelanggan:',
    'customerInformation.contactCustomer.info.telephone': 'Telepon:',
    'customerInformation.contactCustomer.info.emailAddress': 'Email:',
    'customerInformation.contactCustomer.info.channelName': 'Saluran:',
    'customerInformation.contactCustomer.info.customerGroupName': 'Kelompok:',
    'customerInformation.contactCustomer.info.mailingAddress': 'Alamat surat:',
    'customerInformation.contactCustomer.channel.placeholder': 'Silakan pilih saluran',
    'customerInformation.contactCustomer.subject.placeholder': 'Silakan masukkan subjek email',
    'customerInformation.contactCustomer.send.button': 'Kirim',
    'customerInformation.contactCustomer.table.workRecordId': 'ID Catatan',
    'customerInformation.contactCustomer.table.channelName': 'Nama saluran',
    'customerInformation.contactCustomer.table.customerContactInfo': 'Informasi kontak pelanggan',
    'customerInformation.contactCustomer.table.userName': 'Nama agen',
    'customerInformation.contactCustomer.table.status': 'Status',
    'customerInformation.contactCustomer.table.createTime': 'Waktu pembuatan',
    'customerInformation.contactCustomer.table.resolveTime': 'Waktu penyelesaian',
    'customerInformation.contactCustomer.button.send': 'Kirim',
    'customerInformation.contactCustomer.record': 'Riwayat Komunikasi',
    'customerInformation.name.not.empty': 'Nama depan pelanggan pada baris {lineNum} tidak boleh kosong',
    'customerInformation.name.length.limit': 'Nama depan pelanggan pada baris {lineNum} tidak boleh melebihi 200 karakter',
    'customerInformation.name.input.limit': 'Nama depan pelanggan pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.last.name.not.empty': 'Nama belakang pelanggan pada baris {lineNum} tidak boleh kosong',
    'customerInformation.last.name.length.limit': 'Nama belakang pelanggan pada baris {lineNum} tidak boleh melebihi 200 karakter',
    'customerInformation.last.name.input.limit': 'Nama belakang pelanggan pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.group.not.empty': 'Nama kelompok pada baris {lineNum} tidak boleh kosong',
    'customerInformation.channel.not.empty': 'Saluran sumber pada baris {lineNum} tidak boleh kosong',
    'customerInformation.phone.input.limit': 'Nomor telepon seluler pada baris {lineNum} memiliki format yang salah, hanya angka yang diperbolehkan',
    'customerInformation.phone.length.limit': 'Nomor telepon seluler pada baris {lineNum} tidak boleh melebihi 40 karakter',
    'customerInformation.phone.prefix.limit': 'Kode panggilan internasional pada baris {lineNum} salah, silakan ubah',
    'customerInformation.company.input.limit': 'Nama perusahaan pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.company.length.limit': 'Nama perusahaan pada baris {lineNum} tidak boleh melebihi 80 karakter',
    'customerInformation.post.input.limit': 'Jabatan pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.post.length.limit': 'Posisi pada baris {lineNum} tidak boleh melebihi 80 karakter',
    'customerInformation.mailingAddress.input.limit': 'Alamat surat pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.mailingAddress.length.limit': 'Alamat surat pada baris {lineNum} tidak boleh melebihi 200 karakter',
    'customerInformation.orderAddress.input.limit': 'Alamat tagihan pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.orderAddress.length.limit': 'Alamat tagihan pada baris {lineNum} tidak boleh melebihi 200 karakter',
    'customerInformation.deliveryAddress.input.limit': 'Alamat pengiriman pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.deliveryAddress.length.limit': 'Alamat pengiriman pada baris {lineNum} tidak boleh melebihi 200 karakter',
    'customerInformation.otherAddress.input.limit': 'Alamat lainnya pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.otherAddress.length.limit': 'Alamat lainnya pada baris {lineNum} tidak boleh melebihi 200 karakter; Catatan pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.remark.input.limit': 'Catatan pada baris {lineNum} hanya boleh berisi huruf Cina, Inggris, angka, dan "."',
    'customerInformation.remark.length.limit': 'Catatan pada baris {lineNum} tidak boleh melebihi 200 karakter',
    'customerInformation.repeat.phone.tips': 'Ada nomor telepon seluler yang duplikat dalam file: {repeatPhone}',
    'customerInformation.repeat.email.tips': 'Ada email yang duplikat dalam file: {repeatEmail}',
    'customerInformation.batchImport': 'Impor batch',
    'customerInformation.export': 'Ekspor',
    'customerInformation.download.template': 'Unduh template',
    'customerInformation.add.basicInformation.customerLevel': 'Level:',
    'customerInformation.add.basicInformation.customerLevel.placeholder': 'Silakan pilih level pelanggan',
    'customerInformation.add.basicInformation.customerLevel.required': 'Silakan pilih level pelanggan',
    'customerInformation.add.basicInformation.customer.language': 'Bahasa',
    'customerInformation.add.basicInformation.tags': 'Tag:',
    'customerInformation.add.basicInformation.tags.placeholder': 'Silakan masukkan tag dan tekan Enter',
    'customerInformation.modal.batch.change.grouping': 'Ubah kelompok secara batch',
    'customerInformation.modal.batch.import': 'Impor batch',
    'hotWord.length': 'Panjang tag terlalu panjang',
    'customerInformation.upload.file': 'Hanya mendukung mengunggah satu file, klik pada <b>{US}</b>',
    'knowledge.QA.upload.file.request': '1. Format Excel mendukung *. xls, * XLSX dalam dua format',
    'customerInformation.upload.btn': 'Seret dan lepas file di sini atau<b>{US}</b>',
    'customerInformation.upload.download': 'Klik untuk mengunduh',
    'customerInformation.upload.file.1': 'Deskripsi dokumen: ',
    'customerInformation.option.error': 'Pilih setidaknya satu data yang dikelompokkan!',
    'user.management.operation.table.sourceType.1': 'Dibuat secara otomatis',
    'user.management.operation.table.sourceType.2': 'Dibuat secara manual',
    'user.management.operation.table.sourceType.3': 'Pengenalan pemasaran',
    'user.management.operation.table.btn.1': 'Tambah tag secara batch',
    'user.management.operation.table.btn.2': 'Hapus tag secara batch',
    'user.management.operation.modal.tags.placeholder': 'Silakan masukkan tag pelanggan, tekan Enter untuk menambah lebih banyak, Anda dapat memasukkan beberapa',
    'customerInformation.create.customer.email.tips': 'Panjang penerima, Cc, dan Bcc tidak boleh melebihi 2000 karakter!',
    'customerInformation.create.customer.email.content.tips': 'Konten email tidak boleh kosong!',
    'customerInformation.add.basicInformation.tags.tips.new': 'Silakan pilih atau buat setidaknya satu tag!',
};
