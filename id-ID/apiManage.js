export default {
    'ai.agent.api.modal.title': 'Manajemen API',
    'ai.agent.api.table.operation.add': '<PERSON>bah',
    'ai.agent.api.table.key.placeholder': '<PERSON><PERSON>kka<PERSON> kunci',
    'ai.agent.api.table.value.placeholder': '<PERSON><PERSON>kkan nilai',
    'ai.agent.api.table.description.placeholder': '<PERSON><PERSON><PERSON><PERSON> deskripsi',
    'ai.agent.api.table.description.title': '<PERSON><PERSON><PERSON><PERSON>',
    'ai.agent.api.table.operation.title': 'Operasi',
    'ai.agent.api.search.placeholder': 'Cari',
    'ai.agent.api.operation.add': 'Tambah Antarmuka API',
    'ai.agent.api.table.empty.text': 'Anda belum menambahkan antarmuka API apa pun',
    'ai.agent.api.url.placeholder': 'Masukkan alamat antarmuka API',
    'ai.agent.api.operation.test.btn': 'U<PERSON>',
    'ai.agent.api.operation.save.btn': 'Simpan',
    'ai.agent.api.response.title': '<PERSON><PERSON>',
    'ai.agent.script.settings.not.empty.tips': 'Silakan simpan API yang sedang diedit',
    'ai.agent.api.url.placeholder.tips': 'Silakan masukkan alamat antarmuka API',
    'ai.agent.api.authentication.open': 'Aktifkan autentikasi API',
    'ai.agent.api.authentication.type': 'Jenis autentikasi API',
    'ai.agent.api.authentication.header.placeholder': 'Masukkan Header',
    'ai.agent.api.authentication.apiKey.placeholder': 'Masukkan API Key',
    'ai.agent.api.authentication.basic': 'Basic',
    'ai.agent.api.authentication.bearer': 'Bearer',
    'ai.agent.api.authentication.custom': 'Kustom',
};
