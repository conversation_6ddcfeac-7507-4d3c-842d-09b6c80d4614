export default {
    'workRecord.customer.link.way': 'Informasi kontak pelanggan',
    'common.total.items': 'Total {total} item',
    'message.upload.size': 'Ukuran file yang diunggah tidak boleh melebihi {fileSize}MB',
    'work.record.channel.name': '<PERSON><PERSON>',
    'work.record.time.range': 'Rentang waktu',
    'work.record.waiting.handle': 'Tertunda',
    'work.record.in.process': 'Sedang berlangsung',
    'work.record.resolved': 'Terselesaikan',
    'work.record.id': 'ID Catatan',
    'work.record.customer.name': '<PERSON>a pelanggan',
    'work.record.customer.link.way': 'Informasi kontak pelanggan',
    'work.record.settle.name': 'Nama agen',
    'work.record.status': 'Status',
    'work.record.create.time': 'Waktu pembuatan',
    'work.record.resolve.time': 'Waktu penyelesaian',
    'work.record.operation': '<PERSON><PERSON><PERSON>',
    'work.record': 'Tiket',
    'work.record.detail': 'Detail',
    'work.record.reply': 'Balas',
    'work.record.mark.as.in.process': 'Tandai sedang berlangsung',
    'work.record.mark.as.resolved': 'Tandai sebagai terselesaikan',
    'work.record.note': 'Catatan',
    'work.record.button.ok': 'Konfirmasi',
    'work.record.button.cancel': 'Batal',
    'work.record.customer.info': 'Informasi pelanggan',
    'work.record.return': 'Kembali',
    'work.record.channel': 'Saluran',
    'work.record.start.time': 'Waktu mulai',
    'work.record.button.save': 'Simpan',
    'work.record.recipient': 'Penerima',
    'work.record.caller': 'Penelepon',
    'work.record.voice.record': 'Rekaman suara',
    'work.record.tips.note.limit': 'Silakan masukkan konten catatan, dengan maksimal 2000 karakter',
    'work.record.tips.customer.search.enter': 'Silakan masukkan informasi kontak pelanggan dan tekan Enter untuk mencari',
    'work.record.tips.note.settle.search.enter': 'Silakan masukkan nama agen dan tekan Enter untuk mencari',
    'work.record.tips.note.keyword.enter': 'Silakan masukkan informasi seperti ID catatan, informasi kontak pelanggan, atau konten kolom dinamis dan tekan Enter untuk mencari.',
    'work.record.tips.note.content.not.empty': 'Untuk menyimpan, catatan tidak boleh kosong!',
    'work.record.tips.note.content.not.empty.string': 'Konten catatan tidak boleh berupa string kosong!',
    'work.record.tips.note.content.num.limit': 'Konten catatan dapat memiliki maksimal 2000 karakter!',
    'work.record.tips.content.not.empty.enter': 'Silakan masukkan konten non-kosong dan tekan Enter untuk mencari!',
    'work.record.tips.content.num.limit': 'Panjang konten tidak boleh melebihi 80 karakter!',
    'work.record.tips.customer.link.way.not.empty.string': 'Kotak input informasi kontak pelanggan tidak boleh berupa string kosong!',
    'work.record.tips.settle.not.empty.string': 'Kotak input nama agen tidak boleh berupa string kosong!',
    'work.record.tips.keyword.not.empty.string': 'Konten kotak pencarian tidak boleh berupa string kosong!',
    'work.record.tips.at.least.one.status': 'Silakan pilih setidaknya satu status!',
    'work.record.detail.contact.search.copy': 'Pencarian Kontak sesuai dengan',
    'work.record.reply.content.not.empty': 'Konten balasan tidak boleh kosong!',
    'work.record.reply.success': 'Balasan berhasil!',
    'work.record.detail.copy.success': 'Berhasil disalin!',
    'work.record.channel.type': 'Silakan pilih jenis saluran',
};
