export default {
    'joinUs.title': 'joinUs.form.submit'
:
'<PERSON><PERSON>', 'joinUs.form.name.required'
:
'<PERSON><PERSON><PERSON> masukkan nama <PERSON>', 'joinUs.form.name.placeholder'
:
'<PERSON><PERSON><PERSON> masukkan nama <PERSON>', 'joinUs.form.name.max'
:
'Panjang nama tidak boleh melebihi 80 karakter', 'joinUs.form.surname.required'
:
'<PERSON><PERSON>an masukkan nama belakang <PERSON>a', 'joinUs.form.surname.placeholder'
:
'<PERSON><PERSON>an masukkan nama belakang <PERSON>a', 'joinUs.form.surname.max'
:
'Panjang nama belakang tidak boleh melebihi 80 karakter', 'joinUs.form.companyName.required'
:
'<PERSON>lakan masukkan nama perusahaan Anda', 'joinUs.form.companyName.placeholder'
:
'Silakan masukkan nama perusahaan Anda', 'joinUs.form.companyName.max'
:
'Panjang nama perusahaan tidak boleh melebihi 80 karakter', 'joinUs.form.companyEmail.required'
:
'<PERSON><PERSON><PERSON> masukkan email perusa<PERSON><PERSON>', 'joinUs.form.companyEmail.placeholder'
:
'<PERSON>lakan masukkan email perusahaan <PERSON>', 'joinUs.form.companyEmail.max'
:
'Panjang email perusahaan tidak boleh melebihi 255 karakter', 'joinUs.form.phone.required'
:
'Silakan masukkan nomor telepon Anda', 'joinUs.form.phone.placeholder'
:
'Silakan masukkan nomor telepon Anda', 'joinUs.form.phone.max'
:
'Panjang nomor telepon tidak boleh melebihi 15 karakter', 'joinUs.form.message.required'
:
'Silakan masukkan pesan Anda', 'joinUs.form.message.placeholder'
:
'Silakan masukkan pesan Anda', 'joinUs.form.message.max'
:
'Panjang pesan tidak boleh melebihi 2000 karakter', 'joinUs.form.submit.success'
:
'Berhasil dikirim',
}
;
