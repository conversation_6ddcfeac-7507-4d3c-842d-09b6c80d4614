export default {
    'aiAgentLibrary.banner.title': '<PERSON><PERSON>',
    'aiAgentLibrary.banner.subtitle': 'Agen AI ConnectNow',
    'aiAgentLibrary.tab.list.1': 'Se<PERSON><PERSON>',
    'aiAgentLibrary.tab.list.2': '<PERSON><PERSON><PERSON>',
    'aiAgentLibrary.tab.list.3': 'Energi <PERSON>u',
    'aiAgentLibrary.tab.list.4': 'Gaming',
    'aiAgentLibrary.tab.list.5': 'E-commerce',
    'aiAgentLibrary.tab.list.6': 'Manufaktur',
    'aiAgentLibrary.agent.list.content.1.title': 'Titik Nyeri <PERSON>',
    'aiAgentLibrary.agent.list.content.2.title': 'Solusi Agen AI',
    'aiAgentLibrary.agent.list.content.3.title': 'Dialog Contoh',
    'aiAgentLibrary.agent.list.content.3.trigger.title': '<PERSON><PERSON>',
    'aiAgentLibrary.agent.list.1.title': 'Penasihat <PERSON>du<PERSON>',
    'aiAgentLibrary.agent.list.1.industry': '<PERSON><PERSON><PERSON>',
    'aiAgentLibrary.agent.list.1.content.1.list.1': 'Klien kesulitan memilih produk keuangan yang tepat dari berbagai pilihan yang sesuai dengan situasi keuangan, toleransi risiko, dan tujuan investasi mereka.',
    'aiAgentLibrary.agent.list.1.content.1.list.2': "Kuesioner penilaian risiko tradisional kaku dan gagal memahami kebutuhan dan kekhawatiran klien yang sebenarnya.",
    'aiAgentLibrary.agent.list.1.content.1.list.3': 'Ambang batas layanan yang tinggi untuk penasihat keuangan mencegah klien biasa menerima saran investasi yang personal dan tepat waktu.',
    'aiAgentLibrary.agent.list.1.content.2.list.1': 'Agen cerdas ini menganalisis modal investasi dan preferensi risiko klien untuk memberikan rekomendasi produk keuangan dalam bahasa alami yang personal.',
    'aiAgentLibrary.agent.list.1.content.3.list.1': "Saya ingin berinvestasi tapi tidak tahu apa yang harus dipilih. Saya memiliki dana siap pakai $100.000.",
    'aiAgentLibrary.agent.list.1.content.3.list.2': "Saya tertarik dengan manajemen kekayaan. Bisakah Anda merekomendasikan sesuatu untuk saya?",
    'aiAgentLibrary.agent.list.2.title': 'Asisten Pengajuan Pinjaman',
    'aiAgentLibrary.agent.list.2.industry': 'Keuangan',
    'aiAgentLibrary.agent.list.2.content.1.list.1': 'Klien tidak memiliki kejelasan tentang kriteria kelayakan pinjaman dan dokumentasi yang diperlukan, mengakibatkan pertanyaan berulang yang memakan waktu.',
    'aiAgentLibrary.agent.list.2.content.1.list.2': 'Petugas bank menghabiskan waktu berlebihan untuk penyaringan awal, pengumpulan dokumen, dan proses verifikasi.',
    'aiAgentLibrary.agent.list.2.content.1.list.3': 'Prosedur pengajuan pinjaman yang panjang dengan transparansi terbatas menyebabkan pengalaman pelanggan yang buruk.',
    'aiAgentLibrary.agent.list.2.content.2.list.1': 'Solusi ini secara efektif mengurangi intervensi manual, mempercepat pemrosesan pinjaman awal, sambil mempertahankan pengalaman layanan yang sangat personal.',
    'aiAgentLibrary.agent.list.2.content.3.list.1': "Saya ingin mengajukan pinjaman renovasi rumah",
    'aiAgentLibrary.agent.list.2.content.3.list.2': "Saya tertarik untuk mengajukan pinjaman",
    'aiAgentLibrary.agent.list.3.title': 'Klaim Asuransi Express',
    'aiAgentLibrary.agent.list.3.industry': 'Keuangan',
    'aiAgentLibrary.agent.list.3.content.1.list.1': 'Proses klaim asuransi yang kompleks membuat klien tidak pasti tentang prosedur setelah sakit atau kecelakaan.',
    'aiAgentLibrary.agent.list.3.content.1.list.2': 'Persyaratan dokumentasi yang rumit membuat klien sulit memahami kriteria kelayakan klaim dalam ketentuan polis.',
    'aiAgentLibrary.agent.list.3.content.1.list.3': 'Kurangnya transparansi dalam proses klaim mencegah klien menerima pembaruan tepat waktu tentang status dan kemajuan.',
    'aiAgentLibrary.agent.list.3.content.2.list.1': 'Solusi ini secara signifikan mengurangi waktu penyelidikan klaim, meningkatkan kepuasan pelanggan, dan mengurangi beban layanan pelanggan, memberikan peningkatan simultan dalam efisiensi layanan asuransi dan pengalaman pelanggan.',
    'aiAgentLibrary.agent.list.3.content.3.list.1': 'Saya perlu mengajukan klaim untuk operasi yang saya jalani minggu lalu. Bagaimana saya melanjutkannya?',
    'aiAgentLibrary.agent.list.3.content.3.list.2': 'Saya ingin mengajukan klaim asuransi',
    'aiAgentLibrary.agent.list.4.title': 'Asisten Pengetahuan Asuransi',
    'aiAgentLibrary.agent.list.4.content.1.list.1': 'Pengetahuan keuangan kompleks dan profesional, sehingga sulit bagi pengguna biasa untuk memahami dan menerapkannya',
    'aiAgentLibrary.agent.list.4.content.1.list.2': 'Sumber pengetahuan keuangan tradisional terfragmentasi, sehingga sulit menemukan jawaban yang tepat',
    'aiAgentLibrary.agent.list.4.content.1.list.3': 'Terminologi keuangan samar dan sulit dipahami',
    'aiAgentLibrary.agent.list.4.content.1.list.4': 'Kebijakan keuangan sering diperbarui, membuat pengguna sulit mengikuti perkembangan terkini',
    'aiAgentLibrary.agent.list.4.content.1.list.5': 'Kurangnya interpretasi pengetahuan keuangan berbasis skenario, menciptakan kesenjangan antara teori dan praktik',
    'aiAgentLibrary.agent.list.4.content.2.list.1': "Agen cerdas ini memanfaatkan basis pengetahuan keuangan komprehensif yang dikombinasikan dengan kemampuan pemahaman bahasa alami LLM untuk memberikan penjelasan pengetahuan keuangan yang jelas dan berbasis skenario, membantu pengguna lebih memahami dan menerapkan konsep keuangan.",
    'aiAgentLibrary.agent.list.4.content.3.list.1': 'Apa itu kepentingan yang dapat diasuransikan? Untuk siapa pemegang polis dapat memiliki kepentingan yang dapat diasuransikan?',
    'aiAgentLibrary.agent.list.4.content.3.list.2': 'Mengapa aplikasi asuransi memerlukan kuesioner kesehatan yang ekstensif, termasuk riwayat medis keluarga?',
    'aiAgentLibrary.agent.list.5.title': 'Asisten Generasi Lead Cerdas',
    'aiAgentLibrary.agent.list.5.industry': 'Keuangan',
    'aiAgentLibrary.agent.list.5.content.1.list.1': 'Pengumpulan lead berbasis formulir tradisional kaku dengan keterlibatan pelanggan yang rendah',
    'aiAgentLibrary.agent.list.5.content.1.list.2': 'Tingkat konversi yang buruk dari pertanyaan ke penangkapan lead',
    'aiAgentLibrary.agent.list.5.content.1.list.3': 'Kualitas informasi lead yang tidak konsisten mempengaruhi efektivitas tindak lanjut',
    'aiAgentLibrary.agent.list.5.content.1.list.4': 'Kurangnya panduan dan interaksi yang dipersonalisasi',
    'aiAgentLibrary.agent.list.5.content.1.list.5': 'Ketidakmampuan untuk mengidentifikasi lead bernilai tinggi secara real-time',
    'aiAgentLibrary.agent.list.5.content.2.list.1': 'Agen cerdas ini mengumpulkan informasi pelanggan melalui dialog alami, menggabungkan interaksi berbasis skenario dan demonstrasi nilai untuk meningkatkan tingkat konversi lead. Ini juga menyusun informasi yang dikumpulkan untuk mendukung inisiatif pemasaran yang tepat.',
    'aiAgentLibrary.agent.list.5.content.3.list.1': "Berapa biaya asuransi penyakit kritis Anda? Saya ingin tahu lebih banyak.",
    'aiAgentLibrary.agent.list.5.content.3.list.2': "Berapa premi untuk asuransi jiwa Anda?",
    // 游戏start
    'aiAgentLibrary.agent.list.game.1.title': 'Asisten Isi Ulang Game',
    'aiAgentLibrary.agent.list.game.1.content.1.list.1': 'Masalah pembayaran adalah jenis pertanyaan layanan pelanggan yang paling umum dalam gaming',
    'aiAgentLibrary.agent.list.game.1.content.1.list.2': 'Menganalisis pembayaran yang gagal kompleks dan memerlukan beberapa langkah pemecahan masalah',
    'aiAgentLibrary.agent.list.game.1.content.1.list.3': 'Layanan pelanggan tradisional kesulitan untuk dengan cepat menganalisis catatan transaksi dan memberikan solusi yang tepat',
    'aiAgentLibrary.agent.list.game.1.content.2.list.1': 'Solusi ini secara cerdas mengidentifikasi masalah pembayaran gaming, secara otomatis menanyakan catatan transaksi, dan memberikan solusi yang dipersonalisasi, secara signifikan mengurangi waktu tunggu pelanggan dan tekanan staf layanan. Sistem menangani masalah umum seperti pembayaran yang tidak dikreditkan, anomali pembayaran, dan memberikan panduan real-time dan pembaruan status, sangat meningkatkan pengalaman layanan pembayaran dan efisiensi resolusi masalah.',
    'aiAgentLibrary.agent.list.game.1.content.3.list.1': "Saya baru saja membayar 100 dolar, tetapi tidak menerima berlian apa pun dalam game. Apa yang terjadi?",
    'aiAgentLibrary.agent.list.game.1.content.3.list.2': "Saya baru saja melakukan pembayaran, tetapi belum dikreditkan ke akun saya",
    'aiAgentLibrary.agent.list.game.2.title': 'Asisten Laporan Bug Game',
    'aiAgentLibrary.agent.list.game.2.content.1.list.1': 'Pemain memberikan deskripsi yang tidak jelas saat melaporkan bug',
    'aiAgentLibrary.agent.list.game.2.content.1.list.2': 'Layanan pelanggan perlu berulang kali menanyakan informasi dasar',
    'aiAgentLibrary.agent.list.game.2.content.1.list.3': 'Kurangnya sistem klasifikasi bug dan prioritas yang terpadu',
    'aiAgentLibrary.agent.list.game.2.content.1.list.4': 'Pemain tidak mengetahui status pemrosesan bug',
    'aiAgentLibrary.agent.list.game.2.content.2.list.1': 'Solusi ini dengan cepat mengumpulkan informasi bug kunci melalui dialog terpandu yang disederhanakan, memungkinkan klasifikasi dan pemrosesan masalah yang efisien. Sistem secara otomatis menetapkan prioritas berdasarkan jenis bug dan memberikan pemain ekspektasi yang jelas tentang proses selanjutnya, secara signifikan meningkatkan pengalaman pengguna dan efisiensi layanan pelanggan.',
    'aiAgentLibrary.agent.list.game.2.content.3.list.1': "Ada bug sistem - penguatan peralatan saya gagal, bahan dan emas dipotong, tetapi peralatan tidak diperkuat.",
    'aiAgentLibrary.agent.list.game.2.content.3.list.2': 'Game sangat lag, benar-benar tidak dapat dimainkan.',
    'aiAgentLibrary.agent.list.game.3.title': 'Ahli Konsultasi Game',
    'aiAgentLibrary.agent.list.game.3.content.1.list.1': 'Banyak acara game dan pembaruan versi yang sering menciptakan permintaan tinggi untuk informasi tentang aturan acara, hadiah, persyaratan partisipasi, dan konten pembaruan. Pengumuman tersebar, sehingga sulit untuk dengan cepat menemukan jawaban.',
    'aiAgentLibrary.agent.list.game.3.content.2.list.1': 'Solusi ini secara cerdas mengidentifikasi pertanyaan tentang acara game dan pembaruan versi, secara otomatis memberikan aturan acara, metode perolehan hadiah, dan informasi pembaruan, secara signifikan mengurangi waktu yang dihabiskan pemain untuk mencari informasi.',
    'aiAgentLibrary.agent.list.game.3.content.3.list.1': 'Apakah ada acara baru baru-baru ini?',
    'aiAgentLibrary.agent.list.game.3.content.3.list.2': 'Apa itu gameplay "Move! Money-Picking Immortal" di Dream Star Park?',
    // 游戏end
    // 新能源 start
    'aiAgentLibrary.agent.list.newEnergy.1.title': 'Konsultasi Peringatan Kerusakan',
    'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.1': 'Pengguna perlu dengan cepat memahami penyebab dan solusi saat menghadapi kerusakan stasiun listrik',
    'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.2': 'Mengurangi beban kerja layanan pelanggan yang berulang kali menjawab pertanyaan kerusakan yang sama',
    'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.3': 'Memberikan prosedur penanganan kerusakan yang terstandarisasi untuk mengurangi risiko kesalahan operasional',
    'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.4': 'Respons real-time 24/7 terhadap kebutuhan konsultasi kerusakan',
    'aiAgentLibrary.agent.list.newEnergy.1.content.2.list.1': 'Solusi ini secara cerdas mengidentifikasi kerusakan dan memberikan prosedur penanganan yang terstandarisasi, secara signifikan meningkatkan efisiensi respons kerusakan, mengurangi beban kerja layanan pelanggan manual, dan mencapai peningkatan ganda dalam efisiensi pemeliharaan dan pengalaman layanan. Respons real-time 24/7 dan layanan interaktif memastikan pengguna menerima panduan yang tepat waktu dan profesional untuk resolusi kerusakan.',
    'aiAgentLibrary.agent.list.newEnergy.1.content.3.list.1': 'Ketika sistem mendeteksi alarm kerusakan stasiun listrik, ia memasuki jendela percakapan tanpa memerlukan konsultasi yang diprakarsai pengguna, langsung mendorong informasi kerusakan dan saran penanganan.',
    'aiAgentLibrary.agent.list.newEnergy.2.title': 'Asisten Upgrade Firmware',
    'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.1': 'Pengguna perlu secara manual meminta layanan upgrade firmware',
    'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.2': 'Identifikasi kebutuhan upgrade firmware untuk pengguna multibahasa',
    'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.3': 'Kebutuhan konfigurasi parameter perangkat (frekuensi pengumpulan, paket bahasa, dll.) tersebar',
    'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.4': 'Kurangnya umpan balik status real-time selama proses upgrade',
    'aiAgentLibrary.agent.list.newEnergy.2.content.2.list.1': 'Solusi ini mengimplementasikan respons cepat terhadap kebutuhan upgrade firmware untuk pengguna multibahasa melalui pengenalan semantik cerdas dan proses otomatis, memberikan umpan balik status real-time yang secara signifikan meningkatkan efisiensi dan pengalaman pengguna dari layanan upgrade perangkat.',
    'aiAgentLibrary.agent.list.newEnergy.2.content.3.list.1': 'Saya perlu mengupgrade firmware',
    'aiAgentLibrary.agent.list.newEnergy.2.content.3.list.2': 'Saya ingin mengupgrade inverter ke versi terbaru',
    'aiAgentLibrary.agent.list.newEnergy.3.title': 'Ahli Pengetahuan Inverter',
    'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.1': 'Pengguna kurang memahami secara mendalam parameter teknis dan fungsi inverter',
    'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.2': 'Informasi diagnosis kerusakan dan pemeliharaan tersebar dan sulit diakses dengan cepat',
    'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.3': 'Keputusan pemilihan produk memerlukan dukungan pengetahuan profesional',
    'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.4': 'Proses instalasi dan konfigurasi untuk berbagai jenis inverter kompleks',
    'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.5': 'Teknologi berkembang pesat, sehingga sulit bagi pengguna untuk mengikuti perkembangan terbaru',
    'aiAgentLibrary.agent.list.newEnergy.3.content.2.list.1': "Solusi ini memberikan dukungan pengetahuan profesional untuk pemilihan, instalasi, konfigurasi, diagnosis kerusakan, dan pemeliharaan inverter dengan membangun basis pengetahuan inverter yang komprehensif dikombinasikan dengan teknologi pengenalan semantik cerdas. Sistem mengidentifikasi kebutuhan spesifik pengguna dan memberikan jawaban teknis yang ditargetkan dan panduan operasional, secara signifikan meningkatkan pemahaman pengguna dan efisiensi dalam menggunakan produk inverter.",
    'aiAgentLibrary.agent.list.newEnergy.3.content.3.list.1': 'Saya baru saja membeli inverter, bagaimana cara menginstalnya?',
    'aiAgentLibrary.agent.list.newEnergy.3.content.3.list.2': 'Kode kerusakan ditampilkan, apa arti kode kerusakan 002?',
    // 新能源 end
    // 电商&零售 start
    'aiAgentLibrary.agent.list.retail.1.title': 'Asisten Pengembalian Tanpa Repot',
    'aiAgentLibrary.agent.list.retail.1.content.1.list.1': 'Kebijakan pengembalian dan penukaran kompleks dan sulit dipahami; pelanggan tidak yakin apakah mereka memenuhi syarat atau dokumen apa yang diperlukan.',
    'aiAgentLibrary.agent.list.retail.1.content.1.list.2': 'Proses aplikasi rumit, melibatkan formulir ekstensif dan unggahan dokumen.',
    'aiAgentLibrary.agent.list.retail.1.content.1.list.3': 'Status pengembalian dan penukaran tidak jelas, dengan timeline pengembalian dana atau penggantian yang tidak diketahui, mengharuskan pelanggan untuk menindaklanjuti berulang kali.',
    'aiAgentLibrary.agent.list.retail.1.content.2.list.1': 'Menyederhanakan proses pengembalian dan penukaran yang kompleks menjadi layanan percakapan. Agen secara proaktif memandu pelanggan melalui aplikasi, mengotomatisasi sebagian besar langkah, dan memberikan pembaruan status real-time.',
    'aiAgentLibrary.agent.list.retail.1.content.3.list.1': 'Saya ingin mengembalikan pembelian saya.',
    'aiAgentLibrary.agent.list.retail.1.content.3.list.2': 'Tolong bantu saya mengembalikan apa yang saya beli.',
    'aiAgentLibrary.agent.list.retail.2.title': 'Pelacak Status Pesanan & Logistik Cerdas',
    'aiAgentLibrary.agent.list.retail.2.content.1.list.1': `"Di mana pesanan saya?" (WISMO) adalah pertanyaan paling umum dalam dukungan e-commerce. Pelanggan harus secara manual menyalin nomor pelacakan untuk memeriksa di situs pihak ketiga, dan pembaruan logistik (misalnya, "Tiba di fasilitas penyortiran") sering kali samar. Selama penundaan, pelanggan menjadi cemas dan sering dibiarkan tanpa mengetahui alasannya.`,
    'aiAgentLibrary.agent.list.retail.2.content.2.list.1': 'Agen ini secara proaktif memberikan pelanggan pembaruan pesanan dan pengiriman yang mudah dipahami. Ini menerjemahkan istilah logistik teknis ke dalam bahasa yang sederhana dan, dalam kasus pengecualian seperti penundaan atau kegagalan pengiriman, secara proaktif menjelaskan penyebab dan menawarkan solusi, mengubah kecemasan pelanggan menjadi kepercayaan.',
    'aiAgentLibrary.agent.list.retail.2.content.3.list.1': 'Lacak pesanan saya.',
    'aiAgentLibrary.agent.list.retail.2.content.3.list.2': 'Di mana paket saya?',
    'aiAgentLibrary.agent.list.retail.3.title': 'Penasihat Pemulihan Keranjang Cerdas',
    'aiAgentLibrary.agent.list.retail.3.content.1.list.1': 'Pelanggan menambahkan item ke keranjang mereka tetapi tidak menyelesaikan pembelian karena berbagai alasan (keraguan harga, biaya pengiriman, keraguan produk, atau gangguan).',
    'aiAgentLibrary.agent.list.retail.3.content.1.list.2': 'Metode pemulihan tradisional (seperti pengingat email) terbatas dan gagal mengatasi kekhawatiran akar pelanggan.',
    'aiAgentLibrary.agent.list.retail.3.content.2.list.1': 'Setelah periode pengabaian keranjang, memulai percakapan yang ramah dan tidak mengganggu melalui platform pesan instan (misalnya, obrolan web, WhatsApp) untuk memahami alasan pengabaian dan menawarkan solusi yang dipersonalisasi (seperti kupon, menjawab pertanyaan, atau mengurangi kekhawatiran) untuk mendorong penyelesaian pesanan.',
    'aiAgentLibrary.agent.list.retail.3.content.3.list.1': 'Secara proaktif mengirim pesan kepada pengguna setelah periode pengabaian keranjang.',
    // 电商&零售 end
    // 制造 start
    //     英语 (English)
    'aiAgentLibrary.agent.list.manufacturing.1.title': ' Asisten Pelaporan Kerusakan Perangkat Cerdas',
    'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.1': ' <b>Transfer Informasi yang Tidak Efisien dan Tidak Akurat:</b> Saat melaporkan kerusakan melalui telepon atau email, pelanggan kesulitan untuk secara akurat menggambarkan masalah teknis, sering kali menghilangkan detail kunci seperti nomor seri perangkat atau kode kesalahan.',
    'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.2': ' <b>Biaya Komunikasi Tinggi:</b> Tim layanan harus berulang kali menindaklanjuti untuk mendapatkan informasi lengkap, menghasilkan siklus diagnostik yang panjang dan kecemasan pelanggan.',
    'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.3': ' <b>Penilaian Awal yang Salah:</b> Mengandalkan deskripsi verbal semata dapat menyebabkan penilaian yang salah tentang tingkat keparahan atau jenis kerusakan, menghasilkan pengiriman teknisi atau suku cadang yang salah.',
    'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.4': ' <b>Tidak Ada Umpan Balik Langsung:</b> Setelah mengirimkan laporan kerusakan, pelanggan tidak menerima pembaruan tepat waktu dan dibiarkan tidak pasti tentang status atau timeline resolusi.',
    'aiAgentLibrary.agent.list.manufacturing.1.content.2.list.1': 'Menyediakan alur kerja pelaporan kerusakan yang interaktif dan cerdas. Melalui panduan percakapan, ini membantu pelanggan memberikan semua informasi yang diperlukan dengan cara yang terstruktur, termasuk model perangkat, gejala kerusakan, dan lingkungan operasi. Agen memahami deskripsi bahasa alami, secara otomatis mengekstrak informasi kunci, melakukan klasifikasi awal dan penilaian prioritas berdasarkan jenis kerusakan, dan menghasilkan tiket layanan yang komprehensif, terfokus, dan berkualitas tinggi.',
    'aiAgentLibrary.agent.list.manufacturing.1.content.3.list.1': 'Saya ingin melaporkan kerusakan.',
    'aiAgentLibrary.agent.list.manufacturing.2.title': 'Asisten Pengetahuan AI Manufaktur',
    'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.1': ' <b>Silo Pengetahuan:</b> Informasi kritis tersebar di manual PDF, dokumen Word, gambar CAD, pikiran pekerja berpengalaman, dan sistem intranet yang ketinggalan zaman, sehingga sulit untuk ditemukan dan digunakan.',
    'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.2': ' <b>Efisiensi Pencarian Rendah:</b> Pencarian kata kunci tradisional tidak dapat memahami konteks istilah teknis; mencari "penggantian bantalan" mungkin menghasilkan pesanan pembelian yang tidak relevan alih-alih panduan pemeliharaan.',
    'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.3': ' <b>Onboarding Lambat:</b> Karyawan baru (terutama teknisi dan insinyur) memerlukan bulan untuk menjadi akrab dengan peralatan dan proses, menghasilkan biaya pelatihan yang tinggi dan ketergantungan pada staf senior untuk bimbingan.',
    'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.4': ' <b>Ketergantungan Ahli:</b> Pengetahuan beberapa ahli kunci tidak ditangkap secara sistematis; ketika mereka pergi, pengalaman berharga hilang, menciptakan hambatan pengetahuan dalam tim.',
    'aiAgentLibrary.agent.list.manufacturing.2.content.2.list.1': 'Membangun portal pengetahuan percakapan yang terpadu. Agen ini tidak hanya mencari tetapi juga memahami kueri bahasa alami, membaca dan mensintesis semua jenis dokumen internal (manual, gambar, laporan, praktik terbaik), dan memberikan jawaban yang tepat dan dapat dilacak sumbernya. Ini bertindak sebagai ahli yang tak kenal lelah dan sangat berpengetahuan tentang semua peralatan dan proses.',
    'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.1': 'Apa perbedaan antara model G3VM-26M10, G3VM-26M11, dan G3VM-66M? Bagaimana saya harus memilih model yang sesuai untuk aplikasi saya?',
    'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.2': 'Apa perbedaan antara VSON(R) dan VSON?',
    'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.3': 'Jika saya ingin memesan relay surface-mount SOP8 untuk lini produksi otomatis, bagaimana mereka dikemas, dan berapa Jumlah Pesanan Minimum (MOQ)?',
    // 制造 end
    // 立即体验
    'aiAgentLibrary.agent.list.button': 'Coba Sekarang',
};
