export default {
    'help.document.title': 'Dokumentasi Bantuan',
    'help.document.whats.app.title': 'Cara mendaftarkan akun bisnis WhatsApp',
    'help.document.line.title': 'Cara mendaftarkan saluran Line',
    'help.document.we.chat.official.title': 'Cara mengonfigurasi informasi saluran akun resmi WeChat',
    'help.document.line.left.menu': 'Buat akun resmi Line',
    'help.document.line.left.menu.1': 'Dapatkan informasi konfigurasi saluran Line',
    'help.document.line.left.menu.2': 'Integrasikan Line ke ConnectNow',
    'help.document.line.step.1.title': 'Langkah 1:',
    'help.document.line.step.2.title': 'Langkah 2:',
    'help.document.line.step.3.title': 'Langkah 3:',
    'help.document.line.step.4.title': 'Langkah 4:',
    'help.document.line.step.5.title': '<PERSON><PERSON>h 5:',
    'help.document.line.step.6.title': '<PERSON><PERSON><PERSON> 6:',
    'help.document.line.step.7.title': '<PERSON><PERSON><PERSON> 7:',
    'help.document.line.step.9.title': '<PERSON><PERSON><PERSON> 8:',
    'help.document.line.step.10.title': 'Langkah 9:',
    'help.document.line.step.1.text.1': 'Buka tautan: <a>https:  '
    help
    .document.line.step
    .1
    .text
    .2':    '
    Pilih untuk
    masuk dengan
    akun Line
    atau akun
    bisnis',  '
    help
    .document.line.step
    .1
    .text
    .3':    '
    Jika tidak
    yakin mana
    yang harus
    digunakan,
    silakan merujuk
    pada penjelasan
    di situs
    resmi Line
:
<a>"apa perbedaan antara masuk dengan akun Line dan masuk dengan akun bisnis?"</a>
',  '
help.document.line.step
.1.text
.4
':    '
Setelah
masuk, isi
informasi
akun
resmi
Line, tinjau
"persyaratan layanan akun resmi Line"
dan
kemudian
klik
"konfirmasi"
',  '
help.document.line.step
.1.text
.5
':    '
Setelah
mengonfirmasi
bahwa
konten
yang
dimasukkan
sudah
benar, klik
"kirim"
',  '
help.document.line.step
.1.text
.6
':    '
Jika
layar
berikut
ditampilkan, artinya
aplikasi
akun
resmi
Line
berhasil
',  '
help.document.line.step
.2.text
':    '
Jika
Anda
sudah
memiliki
akun
resmi
Line, klik < a > manajer
akun
resmi
Line < /a> untuk masuk',  'help.document.line.step.2.text.1':    'Setelah masuk ke "manajer akun resmi Line", pertama pilih akun resmi Line untuk diintegrasikan:',  'help.document.line.step.2.text.2': 'Klik "pengaturan"',  'help.document.line.step.2.text.3':    'Pilih "API pesan" di sebelah kiri, kemudian di halaman API pesan ini, klik "aktifkan API pesan"',  'help.document.line.step.2.text.4':    'Setelah mengklik "aktifkan API pesan", Anda pertama harus memilih penyedia layanan yang ada, atau membuat penyedia layanan baru (penyedia). Setelah pemilihan, silakan baca "persyaratan layanan API akun resmi Line" dengan saksama dan kemudian klik "setuju".',  'help.document.line.step.2.text.5':    'Selanjutnya, isi "URL untuk kebijakan privasi dan persyaratan layanan (opsional)". Jika tidak diisi, Anda masih dapat mengklik "konfirmasi" untuk melanjutkan ke langkah berikutnya',  'help.document.line.step.2.text.6':    'Setelah mengonfirmasi bahwa "nama akun" dan "nama penyedia layanan" sudah benar, klik "konfirmasi" untuk secara resmi mengaktifkan API pesan',  'help.document.line.step.2.text.7':    'Setelah mengklik "konfirmasi", layar berikut akan muncul dan statusnya akan menjadi "digunakan"',  'help.document.line.step.2.text.8':    'Selanjutnya, Anda dapat mengklik "pengembang Line" untuk mendapatkan lebih banyak data yang diperlukan untuk integrasi!',  'help.document.line.step.2.text.9':    'Untuk mengintegrasikan Line ke backend ConnectNow, Anda akan memerlukan 5 data berikut:',  'help.document.line.step.2.text.10': '- Nama aplikasi',  'help.document.line.step.2.text.11': '- ID saluran',  'help.document.line.step.2.text.12': '- Rahasia saluran',  'help.document.line.step.2.text.13': '- Token akses saluran (berlaku lama)',  'help.document.line.step.2.text.14': '- ID akun resmi Line',  'help.document.line.step.2.text.15':    'Di halaman API pesan, klik untuk masuk ke pengembang Line',  'help.document.line.step.2.text.16':    'Tip: ID saluran dan rahasia saluran akan ditampilkan di halaman API pesan, dan Anda juga dapat menyalin dua informasi ini dari halaman ini',  'help.document.line.step.2.text.17':    'Setelah masuk ke pengembang Line, pertama klik avatar di sudut kanan atas, dan pilih akun Line',  'help.document.line.step.2.text.18':    'Dari bagian admin di sebelah kiri, pilih penyedia',  'help.document.line.step.2.text.19':    'Kemudian klik saluran (akun resmi) untuk masuk ke halaman pengaturan',  'help.document.line.step.2.text.20':    'Setelah masuk ke halaman pengaturan, dapatkan data yang diperlukan untuk koneksi dari tab "pengaturan dasar" dan "API pesan" masing-masing',  'help.document.line.step.2.text.21':    'Tiga item pertama dapat diperoleh di "pengaturan dasar"',  'help.document.line.step.2.text.22': 'Beralih ke tab API pesan',  'help.document.line.step.2.text.23':    'Gulir ke bawah halaman, klik tombol keluaran untuk mendapatkan token akses saluran (berlaku lama)',  'help.document.line.step.2.text.24':    'Setelah mengklik, Anda dapat melihat informasi token akses saluran (berlaku lama)',  'help.document.line.step.2.text.25': 'Cara mendapatkan ID akun resmi Line',  'help.document.line.step.2.text.26':    'Masuk ke backend akun resmi Line: <a>https:  'help.document.line.step.2.text.27':    'Salin teks yang disorot merah',
}
;
