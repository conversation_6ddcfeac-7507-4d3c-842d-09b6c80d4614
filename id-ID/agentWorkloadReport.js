export default {
    'agentWorkloadReport.title': '<PERSON><PERSON><PERSON>',
    'agentWorkloadReport.title.admin': '<PERSON><PERSON><PERSON>ban <PERSON>',
    'agentWorkloadReport.title.right': '<PERSON><PERSON>h waktu:    ',
    'agentWorkloadReport.card.1.title': 'Jumlah total tiket',
    'agentWorkloadReport.card.title.tips.1': 'Hitung jumlah total tiket dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.2': 'Hitung jumlah tiket yang belum terselesaikan (termasuk status: <PERSON><PERSON><PERSON>, Belum Ditugaskan) dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.3': 'Lakukan analisis mendalam pada data tiket untuk mengungkap informasi dan tren berharga, termasuk volume tiket tertinggi, terendah, meningkat, dan menurun',
    'agentWorkloadReport.card.title.tips.4': 'Identifikasi tiga agen teratas dengan jumlah tiket tertinggi dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.5': 'Identifikasi tiga agen teratas dengan jumlah tiket terendah dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.6': 'Identifikasi tiga agen teratas dengan peningkatan tiket terbanyak dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.7': 'Identifikasi tiga agen teratas dengan penurunan tiket terbanyak dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.8': 'Hitung jumlah total tiket yang ditangani setiap agen, termasuk tiket Sedang Dikerjakan, Terselesaikan, Dihentikan, dan Dialihkan, dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.9': 'Tampilkan tren volume tiket untuk agen yang berbeda dari waktu ke waktu, dan tunjukkan perubahan tren untuk lima agen',
    'agentWorkloadReport.card.title.tips.10': 'Hitung jumlah tiket berdasarkan jenis saluran untuk setiap agen dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.11': 'Hitung jumlah tiket berdasarkan status dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.12': 'Hitung jumlah tiket berdasarkan jenis dalam periode waktu tertentu',
    'agentWorkloadReport.card.title.tips.13': 'Hitung jumlah tiket berdasarkan prioritas dalam periode waktu tertentu',
    'agentWorkloadReport.card.1.tips': 'Dibandingkan dengan sebelumnya ',
    'agentWorkloadReport.card.2.tips': ' hari',
    'agentWorkloadReport.card.2.title': 'Jumlah total tiket yang belum terselesaikan',
    'agentWorkloadReport.card.3.title': 'Wawasan Data',
    'agentWorkloadReport.card.4.title': 'Top 3 volume tiket tertinggi',
    'agentWorkloadReport.card.5.title': 'Top 3 volume tiket terendah',
    'agentWorkloadReport.card.6.title': 'Top 3 peningkatan volume tiket',
    'agentWorkloadReport.card.7.title': 'Top 3 penurunan volume tiket',
    'agentWorkloadReport.card.table.1': 'Nama tempat duduk',
    'agentWorkloadReport.card.table.2': 'Jumlah yang diproses',
    'agentWorkloadReport.card.table.3': 'Peningkatan jumlah',
    'agentWorkloadReport.card.table.4': 'Penurunan jumlah',
    'agentWorkloadReport.card.8.title': 'Jumlah total tiket yang ditangani agen',
    'agentWorkloadReport.card.8.title.group': 'Jumlah total tiket yang ditangani kelompok agen',
    'agentWorkloadReport.card.9.title': 'Tren volume tiket',
    'agentWorkloadReport.card.10.title': 'Distribusi tiket berdasarkan jenis saluran',
    'agentWorkloadReport.card.11.title': 'Distribusi status tiket',
    'agentWorkloadReport.card.12.title': 'Distribusi jenis tiket',
    'agentWorkloadReport.card.13.title': 'Distribusi prioritas tiket',
    'agentWorkloadReport.card.select.1': 'Nama tempat duduk:',
    'agentWorkloadReport.card.select.1.placeholder': 'Bandingkan data di beberapa tempat duduk',
    'agentWorkloadReport.table.1.title': 'Jumlah total tiket',
    'agentWorkloadReport.table.2.title': 'Waktu',
    'agent.work.report.data.insight': 'Selama periode ini, agen {highestName} menangani jumlah tiket tertinggi, dengan total {highestNumber} tiket yang diproses. Tolong dorong mereka untuk terus mempertahankan kinerja. Agen {minimumName} menangani jumlah tiket terendah, hanya {minimumNumber} tiket yang diproses. Tolong perhatikan situasi kerjanya.',
    'agent.work.report.data.insight.shangsheng': 'Agen {riseHighestName} memiliki peningkatan jumlah tiket tertinggi, dengan peningkatan {riseHighestNumber} tiket. Tolong dorong mereka untuk mempertahankan kinerja ini.',
    'agent.work.report.data.insight.xiajiang': 'Agen {declineHighestName} memiliki penurunan jumlah tiket tertinggi, dengan penurunan {declineHighestNumber} tiket. Tolong perhatikan situasi kerjanya.',
    'agent.work.report.data.insight.admin': 'Selama periode ini, kelompok agen {highestName} menangani jumlah tiket tertinggi, dengan total {highestNumber} tiket yang diproses. Tolong dorong mereka untuk terus mempertahankan kinerja. Kelompok agen {minimumName} menangani jumlah tiket terendah, hanya {minimumNumber} tiket yang diproses. Tolong perhatikan situasi kerjanya.',
    'agent.work.report.data.insight.admin.1': 'Kelompok agen {riseHighestName} memiliki peningkatan jumlah tiket tertinggi, dengan peningkatan {riseHighestNumber} tiket. Tolong dorong mereka untuk mempertahankan kinerja ini.',
    'agent.work.report.data.insight.admin.2': 'Kelompok agen {declineHighestName} memiliki penurunan jumlah tiket tertinggi, dengan penurunan {declineHighestNumber} tiket. Tolong perhatikan situasi kerjanya.',
    'agent.work.report.echarts.legend.1': 'Tiket terselesaikan',
    'agent.work.report.echarts.legend.2': 'Tiket belum terselesaikan',
    'agent.work.report.echarts.legend.3': 'Jumlah tiket yang menunggu respons pelanggan',
    'agent.work.machine.report.data.insight': 'Selama periode ini, robot cerdas membantu Anda menangani {robotWorkTotalNumber} tiket, yang {robotContrastArtificialNumber} {trend1} daripada tiket agen; dibandingkan dengan {daysBetween} hari sebelumnya, itu {trend2} sebesar {robotContrastTimeNumber} tiket. Pertahankan kerja yang baik.',
    'trend.1': 'lebih banyak',
    'trend.2': 'lebih sedikit',
    'trend.3': 'meningkat',
    'trend.4': 'menurun',
    'machineWorkloadReport.title': 'Laporan Tiket Chatbot',
    'machineWorkloadReport.card.1.title': 'Jumlah total tiket chatbot',
    'machineWorkloadReport.card.2.title': 'Wawasan data',
    'machineWorkloadReport.card.3.title': 'Jumlah total tiket chatbot berdasarkan jenis saluran',
    'machineWorkloadReport.card.4.title': 'Distribusi tiket chatbot berdasarkan jenis saluran',
    'machineWorkloadReport.card.5.title': 'Chatbot vs. Agen',
    'machineWorkloadReport.card.6.title': 'Proporsi tiket chatbot',
    'machineWorkloadReport.table.date': 'Waktu',
    'machineWorkloadReport.table.WEB': 'Obrolan web',
    'machineWorkloadReport.table.WhatsApp': 'WhatsApp',
    'machineWorkloadReport.table.APP': 'Obrolan APP',
    'machineWorkloadReport.table.machine.work.order.num': 'Tiket chatbot',
    'machineWorkloadReport.table.artificial.work.order.num': 'Tiket agen',
    'machineWorkloadReport.channel.web': 'Obrolan web',
    'machineWorkloadReport.channel.app': 'Obrolan app',
    'machineWorkloadReport.channel.phone': 'Panggilan masuk',
    'machineWorkloadReport.card.1.tips': 'Dibandingkan dengan 15 hari sebelumnya',
    'machineWorkloadReport.card.1.title.tips': 'Hitung jumlah total tiket yang ditangani robot dalam periode waktu tertentu',
    'machineWorkloadReport.card.2.title.tips': 'Lakukan analisis mendalam berdasarkan data tiket yang ditangani chatbot untuk mendapatkan data perbandingan antara robot dan manusia',
    'machineWorkloadReport.card.3.title.tips': 'Hitung jumlah total tiket yang ditangani robot dalam jenis saluran yang berbeda dalam periode waktu tertentu dan laporkan tren naik turun dari periode sebelumnya',
    'machineWorkloadReport.card.4.title.tips': 'Laporkan perubahan jumlah tiket yang ditangani robot dalam jenis saluran yang berbeda dalam periode waktu tertentu',
    'machineWorkloadReport.card.5.title.tips': 'Bandingkan jumlah tiket yang ditangani robot dengan yang ditangani manusia dalam periode waktu tertentu',
    'machineWorkloadReport.card.6.title.tips': 'Hitung proporsi tiket yang ditangani robot dalam periode waktu tertentu',
};
