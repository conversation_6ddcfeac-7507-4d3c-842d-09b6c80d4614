export default {
    'feedbackPerformance.title': '<PERSON><PERSON><PERSON> Balik Chatbot AIGC',
    'feedbackPerformance.card.3': '5 pertanyaan yang paling disukai',
    'feedbackPerformance.card.4': '5 pertanyaan yang paling tidak disukai',
    'feedbackPerformance.card.1.tabs.1': 'Tingkat suka',
    'feedbackPerformance.card.1.tabs.2': 'Tingkat tidak suka',
    'feedbackPerformance.card.1.tabs.3': 'Tingkat interaksi',
    'feedbackPerformance.card.2.text': 'Selama periode ini, pertanyaan "{question}" menerima jumlah ketidaksukaan tertinggi, dengan total {number}. Harap perhatikan apakah perlu memperbarui dan memelihara pengetahuan.',
    'feedbackPerformance.card.2.text.up': 'Peningkatan',
    'feedbackPerformance.card.2.text.down': 'Penurunan',
    'feedbackPerformance.card.5.text.up': 'Suka',
    'feedbackPerformance.card.5.text.down': 'Tidak Suka',
    'feedbackPerformance.card.5.select': '<PERSON><PERSON>h saluran:',
    'feedbackPerformance.card.5.select.placeholder': '<PERSON><PERSON><PERSON> pilih saluran Anda (beberapa saluran akan digabungkan secara otomatis).',
    'feedbackPerformance.card.5': 'Grafik tren interaksi suka/tidak suka',
    'feedbackPerformance.card.6': 'Jumlah interaksi suka/tidak suka di berbagai saluran',
    'feedbackPerformance.card.1.tips.1': 'Hitung proporsi pertanyaan yang menerima suka di antara semua pertanyaan yang dijawab oleh layanan pelanggan cerdas selama periode waktu tertentu. Tingkat suka = jumlah(jumlah suka) / jumlah(jumlah pertanyaan)',
    'feedbackPerformance.card.1.tips.2': 'Hitung proporsi pertanyaan yang tidak disukai di antara semua pertanyaan yang dijawab oleh layanan pelanggan cerdas dalam periode waktu tertentu. Tingkat tidak suka = jumlah(jumlah tidak suka) / jumlah(jumlah pertanyaan).',
    'feedbackPerformance.card.1.tips.3': 'Hitung total jumlah pertanyaan yang dijawab oleh layanan pelanggan cerdas dan jumlah pertanyaan yang disukai atau tidak disukai dalam periode waktu tertentu. Tingkat interaksi = jumlah(jumlah suka + jumlah tidak suka) / jumlah(jumlah pertanyaan).',
    'feedbackPerformance.card.2.tips': 'Berdasarkan pertanyaan yang dijawab oleh layanan pelanggan cerdas, identifikasi dan rangkum data kunci, termasuk pertanyaan dengan jumlah ketidaksukaan tertinggi dan kuantitasnya, dalam periode waktu yang ditentukan.',
    'feedbackPerformance.card.3.tips': 'Hitung 5 pertanyaan teratas dengan jumlah suka terbanyak untuk jawaban yang diberikan oleh layanan pelanggan cerdas selama periode waktu yang ditentukan.',
    'feedbackPerformance.card.4.tips': 'Hitung 5 pertanyaan teratas dengan jumlah ketidaksukaan terbanyak untuk jawaban yang disediakan oleh chatbot AI selama periode waktu yang ditentukan.',
    'feedbackPerformance.card.5.tips': 'Tampilkan tren jumlah suka dan tidak suka pada titik waktu yang berbeda, beberapa saluran dapat ditentukan, dan secara default dihitung untuk semua saluran.',
    'feedbackPerformance.card.6.tips': 'Tampilkan jumlah pertanyaan yang menerima suka dan tidak suka dari berbagai saluran dalam periode waktu yang berbeda',
};
