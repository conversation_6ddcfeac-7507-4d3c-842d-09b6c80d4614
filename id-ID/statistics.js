export default {
    'statistics.work.report.title': 'Laporan beban kerja untuk agen',
    'statistics.work.report.label.1': 'Kelompok agen: ',
    'statistics.work.report.label.1.1': 'Kelompok agen',
    'statistics.work.report.label.2': '<PERSON><PERSON> t<PERSON>: ',
    'statistics.work.report.label.3': '<PERSON><PERSON>h waktu: ',
    'statistics.work.report.label.4': 'Filter saluran',
    'statistics.work.report.label.3.placeholder': '<PERSON><PERSON><PERSON> pilih tanggal',
    'statistics.work.report.label.1.placeholder': 'Silakan pilih kelompok agen',
    'statistics.work.report.label.2.placeholder': '<PERSON><PERSON><PERSON> pilih jenis tiket',
    'statistics.work.report.series.name.delay': '<PERSON><PERSON><PERSON> tiket yang tidak terselesaikan dan terlambat',
    'statistics.work.report.series.name': '<PERSON><PERSON><PERSON> tiket yang tidak terselesaikan',
    'statistics.data.details.title': 'Detail Kontak',
    'statistics.data.details.select.date': '<PERSON><PERSON><PERSON> waktu: ',
    'statistics.data.details.search.tips': '<PERSON><PERSON>an masukkan ID kontak, agen, kelompok agen, atau antrian untuk mencari',
    'statistics.data.details.table.contact.id': 'ID Kontak',
    'statistics.data.details.table.incoming.call.time': 'Waktu mulai',
    'statistics.data.details.table.incoming.call.time.start': 'Waktu respons agen',
    'statistics.data.details.table.end.time': 'Waktu mulai ACW',
    'statistics.data.details.table.acw.time': 'Waktu akhir ACW',
    'statistics.data.details.table.total.call.duration': 'Total durasi',
    'statistics.data.details.table.interaction.time': 'Durasi interaktif',
    'statistics.data.details.table.queue.waiting.time': 'Durasi antrian',
    'statistics.data.details.table.incoming.call.channel': 'Saluran',
    'statistics.data.details.table.reception.seat': 'Agen',
    'statistics.data.details.table.seating.group': 'Kelompok Agen',
    'statistics.data.details.table.queue': 'Antrian',
    'statistics.data.details.table.queue.in': 'Masuk / Keluar',
    'statistics.data.details.table.phone.customer': 'Telepon pelanggan',
    'statistics.data.details.table.acw.duration': 'Durasi ACW',
    'statistics.data.details.table.work.order.number': 'Nomor tiket',
    'statistics.data.details.table.on.hold.time': 'Durasi tahan terlama agen',
    'statistics.data.details.table.on.hold.num': 'Jumlah tahan',
    'statistics.data.details.table.is.switch': 'Transfer',
    'statistics.data.details.table.hanging.type': 'Alasan putus sambungan',
    'statistics.data.details.table.system.phone': 'Telepon sistem',
    'statistics.data.details.table.contact.id.id': 'ID kontak awal',
    'statistics.data.details.table.contact.id.previous': 'ID kontak sebelumnya',
    'statistics.data.details.table.contact.id.next': 'ID kontak berikutnya',
    'statistics.data.details.table.satisfaction.rating': 'Skor kepuasan',
    'statistics.data.details.table.operation': 'Operasi',
    'statistics.data.details.channel.type': 'Jenis saluran: ',
    // Metrik historis agen
    'statistics.agent.performance.indicators.title': 'Metrik Historis Agen',
    'statistics.agent.performance.indicators.tips': 'Silakan masukkan nama agen',
    'statistics.agent.performance.indicators.table.agent.name': 'Nama agen',
    'statistics.agent.performance.indicators.table.accumulated.online.duration': 'Waktu online',
    'statistics.agent.performance.indicators.table.accumulated.idle.duration': 'Waktu idle agen',
    'statistics.agent.performance.indicators.table.accumulated.reception.duration': 'Waktu agen dalam kontak',
    'statistics.agent.performance.indicators.table.task.time.utilization.rate': 'Okupansi',
    'statistics.agent.performance.indicators.table.unresponsive.quantity': 'Agen tidak merespons',
    'statistics.agent.performance.indicators.table.reception.contacts.quantity': 'Kontak yang ditangani',
    'statistics.agent.performance.indicators.table.response.rate': 'Tingkat jawaban agen',
    'statistics.agent.performance.indicators.table.working.hours.after.contact': 'Rata-rata waktu kerja setelah kontak',
    'statistics.agent.performance.indicators.table.customer.retention.time': 'Rata-rata waktu tahan pelanggan',
    // Metrik riwayat antrian
    'statistics.historical.queue.indicators.title': 'Metrik Riwayat Antrian',
    'statistics.historical.queue.indicators.table.queue.name': 'Nama antrian',
    'statistics.historical.queue.indicators.table.queue.name.label': 'Nama antrian: ',
    'statistics.historical.queue.indicators.search.tips': 'Silakan masukkan nama antrian',
    'statistics.historical.queue.indicators.table.working.hours.after.contact': 'Rata-rata waktu kerja setelah kontak',
    'statistics.historical.queue.indicators.table.agent.interaction.time': 'Rata-rata waktu interaksi agen',
    'statistics.historical.queue.indicators.table.customer.retention.time': 'Rata-rata waktu tahan pelanggan',
    'statistics.historical.queue.indicators.table.queue.abandonment.time': 'Rata-rata waktu meninggalkan antrian',
    'statistics.historical.queue.indicators.table.queue.waiting.time': 'Rata-rata waktu jawaban antrian',
    'statistics.historical.queue.indicators.table.abandon.contact.quantity': 'Kontak yang ditinggalkan',
    'statistics.historical.queue.indicators.table.queued.contacts.quantity': 'Kontak dalam antrian',
    // Dimensi antrian
    'statistics.queue.title': 'Metrik Real-Time Antrian',
    'statistics.queue.title.right': 'Waktu pembaruan data: ',
    'statistics.queue.title.1': 'Daftar Status Agen',
    'statistics.queue.title.2': 'Daftar Kontak',
    'statistics.queue.title.3': 'Daftar Kinerja',
    'statistics.queue.title.btn': 'Ekspor batch',
    'statistics.queue.table.agent.1': 'Nama antrian',
    'statistics.queue.table.agent.2': 'Online',
    'statistics.queue.table.agent.3': 'Dalam kontak',
    'statistics.queue.table.agent.4': 'NPT',
    'statistics.queue.table.agent.5': 'ACW',
    'statistics.queue.table.agent.6': 'Error',
    'statistics.queue.table.agent.7': 'Tersedia',
    'statistics.queue.table.contact.2': 'Tersedia (jumlah kontak yang dapat dihubungi)',
    'statistics.queue.table.contact.3': 'Aktif (jumlah kontak yang terhubung)',
    'statistics.queue.table.modal.search': 'Silakan masukkan nama metrik untuk mencari',
    'statistics.queue.table.modal.panel.1': 'Kinerja ({num})',
    'statistics.queue.table.modal.panel.2': 'Kontak Ditinggalkan dalam X ({num}) detik',
    'statistics.queue.table.modal.panel.3': 'Kontak Dijawab dalam X ({num}) detik',
    'statistics.queue.table.modal.panel.4': 'Tingkat Layanan Kontak ({num})',
    'statistics.queue.table.modal.panel.1.check.1': 'Terlama',
    'statistics.queue.table.modal.panel.1.check.2': 'Terjadwal',
    'statistics.queue.table.modal.panel.1.check.3': 'Dalam antrian',
    'statistics.queue.table.modal.panel.1.check.4': 'Ditangani',
    'statistics.queue.table.modal.panel.1.check.5': 'Ditinggalkan',
    'statistics.queue.table.modal.panel.1.check.6': 'AHT',
    'statistics.queue.table.modal.panel.1.check.7': 'Ditangani keluar',
    'statistics.queue.table.modal.panel.1.check.8': 'Kontak API ditangani',
    'statistics.queue.table.modal.panel.1.check.9': 'Kontak callback ditangani',
    'statistics.queue.table.modal.panel.1.check.10': 'Dialihkan keluar dari antrian',
    'statistics.queue.table.modal.panel.1.check.11': 'Rata-rata waktu jawaban antrian',
    'statistics.queue.table.modal.panel.1.check.12': 'Rata-rata waktu meninggalkan',
    'statistics.queue.table.modal.panel.1.check.13': 'Rata-rata Waktu Aktif',
    'statistics.queue.table.modal.panel.1.check.14': 'Rata-rata waktu interaksi dan tahan',
    'statistics.queue.table.modal.panel.1.check.15': 'Rata-rata waktu interaksi',
    'statistics.queue.table.modal.panel.1.check.16': 'Rata-rata waktu resolusi',
    'statistics.queue.table.modal.panel.1.check.17': 'Dalam antrian',
    'statistics.queue.table.modal.panel.1.check.18': 'Dialihkan keluar',
    'statistics.queue.table.modal.panel.1.check.19': 'Maksimal dalam antrian',
    'statistics.queue.table.modal.panel.1.check.20': 'Agen tidak merespons',
    'statistics.queue.table.modal.panel.1.check.21': 'Dialihkan keluar oleh agen',
    'statistics.queue.table.modal.panel.1.check.22': 'Rata-rata Waktu Jeda Agen',
    'statistics.queue.table.modal.panel.1.check.23': 'Rata-rata Waktu Koneksi API',
    'statistics.queue.table.modal.panel.1.check.24': 'Agen tidak merespons',
    'statistics.queue.table.modal.panel.1.check.25': 'Kontak Ditinggalkan dalam {num} detik',
    'statistics.queue.table.modal.panel.1.check.26': 'Kontak Dijawab dalam {num} detik',
    'statistics.queue.table.modal.panel.1.check.25.search': 'Kontak Ditinggalkan dalam',
    'statistics.queue.table.modal.panel.1.check.26.search': 'Kontak Dijawab dalam',
    // Dimensi agen
    'statistics.agent.title': 'Metrik Real-Time Agen',
    'statistics.agent.title.1': 'Daftar Agen',
    'statistics.agent.table.modal.panel.1': 'Agen ({num})',
    'statistics.agent.table.agent.1': 'Nama Agen',
    'statistics.agent.table.agent.2': 'Nama Depan Agen',
    'statistics.agent.table.agent.3': 'Nama Belakang Agen',
    'statistics.agent.table.agent.4': 'Kapasitas',
    'statistics.agent.table.agent.5': 'Nama login agen',
    'statistics.agent.table.agent.5.label': 'Nama login agen: ',
    'statistics.agent.table.agent.5.label.placeholder': 'Silakan masukkan nama login agen',
    'statistics.agent.table.agent.6': 'Aktivitas berikutnya',
    'statistics.agent.table.agent.7': 'Aktivitas',
    'statistics.agent.table.agent.8': 'Durasi',
    'statistics.agent.table.agent.9': 'Saluran',
    'statistics.agent.table.contact.1': 'Antrian',
    'statistics.agent.table.contact.2': 'Durasi',
    'statistics.agent.table.contact.3': 'Status kontak (diterima atau tidak)',
    'statistics.agent.table.contact.4': 'Ketersediaan',
    'statistics.agent.table.modal.panel.1.check.1': 'Ditangani keluar',
    'statistics.agent.table.modal.panel.1.check.2': 'Kontak API ditangani',
    'statistics.agent.table.modal.panel.1.check.3': 'Kontak callback ditangani',
    'statistics.agent.table.modal.panel.1.check.4': 'Rata-rata waktu tahan',
    'statistics.agent.table.modal.panel.1.check.5': 'Rata-rata waktu aktif',
    'statistics.agent.table.modal.panel.1.check.6': 'Rata-rata waktu interaksi',
    'statistics.agent.table.modal.panel.1.check.7': 'Dialihkan keluar',
    'statistics.agent.table.modal.panel.1.check.8': 'Agen tidak merespons',
    'statistics.agent.table.modal.panel.1.check.9': 'Rata-rata waktu jeda agen',
    'statistics.agent.table.modal.panel.1.check.10': 'Rata-rata waktu koneksi API',
    'statistics.agent.table.modal.panel.1.check.11': 'Kontak callback ditangani',
    'statistics.agent.table.modal.panel.1.check.12': 'Dialihkan keluar',
    'statistics.agent.table.modal.panel.1.check.13': 'Rata-rata waktu koneksi masuk',
    // Statistik efisiensi kerja agen
    'agent.work.efficiency.statistics.title': 'Laporan Efisiensi Kerja Agen',
    'agent.work.efficiency.statistics.title.group': 'Laporan Efisiensi Kerja Kelompok Agen',
    'agent.work.efficiency.statistics.second.title.1': 'Rata-rata waktu resolusi',
    'agent.work.efficiency.statistics.second.title.2': 'Wawasan data ',
    'agent.work.efficiency.statistics.data.insight.agent.group': 'Kelompok agen ',
    'agent.work.efficiency.statistics.data.insight.agent': 'Agen ',
    'agent.work.efficiency.statistics.data.insight.agent.group.1': 'Efisiensi rata-rata kelompok agen ',
    'agent.work.efficiency.statistics.data.insight.agent.1': 'Efisiensi rata-rata agen ',
    'agent.work.efficiency.statistics.data.insight.1': 'Selama periode ini, rata-rata waktu pemrosesan tiket oleh agen ',
    'agent.work.efficiency.statistics.data.insight.1.group': 'Selama periode ini, rata-rata waktu pemrosesan tiket oleh kelompok agen ',
    'agent.work.efficiency.statistics.data.insight.2': ' adalah yang terlama, mencapai rata-rata ',
    'agent.work.efficiency.statistics.data.insight.3': '. Silakan perhatikan situasi kerja. Rata-rata waktu pemrosesan tiket oleh ',
    'agent.work.efficiency.statistics.data.insight.4': ' adalah yang terpendek, mencapai rata-rata ',
    'agent.work.efficiency.statistics.data.insight.5': '. Silakan dorong mereka untuk mempertahankan kerja yang baik. ',
    'agent.work.efficiency.statistics.data.insight.6': ' telah meningkat paling banyak, yang telah meningkat sebesar ',
    'agent.work.efficiency.statistics.data.insight.7': '. Silakan dorong mereka untuk mempertahankan kinerja ini. ',
    'agent.work.efficiency.statistics.data.insight.8': ' telah meningkat paling sedikit, yang telah turun sebesar ',
    'agent.work.efficiency.statistics.data.insight.9': ', silakan perhatikan situasi kerja.',
    'agent.work.efficiency.statistics.second.title.3': '3 Teratas waktu resolusi rata-rata terlama ',
    'agent.work.efficiency.statistics.second.title.3.1': '3 Teratas waktu resolusi rata-rata terpendek',
    'agent.work.efficiency.statistics.second.title.3.2': '3 Teratas peningkatan efisiensi',
    'agent.work.efficiency.statistics.second.title.3.3': '3 Teratas penurunan efisiensi',
    'agent.work.efficiency.statistics.agent.name': 'Nama agen',
    'agent.work.efficiency.statistics.agent.name.group': 'Kelompok agen ',
    'agent.work.efficiency.statistics.processing.time': 'Waktu pemrosesan ',
    'agent.work.efficiency.statistics.efficiency.improvement': 'Peningkatan efisiensi',
    'agent.work.efficiency.statistics.efficiency.decline': 'Penurunan efisiensi',
    'agent.work.efficiency.statistics.second.title.4': 'Rata-rata waktu resolusi tiket',
    'agent.work.efficiency.statistics.agent.name.select': 'Nama agen: ',
    'agent.work.efficiency.statistics.agent.name.select.placeholder': 'Beberapa agen dapat dipilih untuk perbandingan data',
    'agent.work.efficiency.statistics.agent.name.select.placeholder.team': 'Anda dapat memilih beberapa kelompok agen untuk perbandingan data',
    'agent.work.efficiency.statistics.table.agent.name': 'Nama agen',
    'agent.work.efficiency.statistics.second.title.5': 'Tren rata-rata waktu resolusi tiket',
    'agent.work.efficiency.statistics.hour.text': 'jam',
    'agent.work.efficiency.statistics.day.text': 'hari',
    'agent.work.efficiency.statistics.week.text': 'minggu',
    'agent.work.efficiency.statistics.month.text': 'bulan',
    'agent.work.efficiency.statistics.second.title.6': 'Distribusi rata-rata waktu resolusi tiket',
    'agent.work.efficiency.statistics.second.title.7': 'Laporan SLA tiket',
    'agent.work.efficiency.statistics.table.time': 'Waktu',
    // Laporan kepuasan
    'satisfaction.report.title': 'Laporan Kepuasan Agen',
    'satisfaction.report.title.group': 'Laporan Kepuasan Kelompok Agen',
    'satisfaction.report.table.rating': 'Peringkat kepuasan',
    'satisfaction.report.second.title.1': 'Skor kepuasan rata-rata',
    'satisfaction.report.second.title.2': 'Rasio kepuasan balasan',
    'satisfaction.report.second.title.3': '3 Teratas skor kepuasan rata-rata tertinggi',
    'satisfaction.report.second.title.4': '3 Teratas skor kepuasan rata-rata terendah',
    'satisfaction.report.second.title.5': '3 Teratas peningkatan kepuasan',
    'satisfaction.report.second.title.6': '3 Teratas penurunan kepuasan',
    'satisfaction.report.second.title.7': 'Skor kepuasan rata-rata',
    'satisfaction.report.second.title.8': 'Tren skor kepuasan rata-rata',
    'satisfaction.report.second.title.9': 'Distribusi skor kepuasan berdasarkan jenis tiket',
    'satisfaction.report.second.title.10': 'Distribusi skor kepuasan berdasarkan jenis saluran',
    'satisfaction.report.second.title.11': 'Rasio kepuasan balasan',
    'satisfaction.report.data.insight.agent.group': 'Skor kepuasan kelompok agen ',
    'satisfaction.report.data.insight.agent': 'Skor kepuasan agen ',
    'satisfaction.report.data.insight.1': 'Selama periode ini, skor kepuasan agen ',
    'satisfaction.report.data.insight.1.group': 'Selama periode ini, skor kepuasan kelompok agen ',
    'satisfaction.report.data.insight.2': ' adalah yang tertinggi, mencapai ',
    'satisfaction.report.data.insight.3': ' poin. ',
    'satisfaction.report.data.insight.4': ' adalah yang terendah, mencapai ',
    'satisfaction.report.data.insight.5': ' poin. ',
    'satisfaction.report.data.insight.6': ' meningkat paling banyak, meningkat sebesar ',
    'satisfaction.report.data.insight.7': ' poin. ',
    'satisfaction.report.data.insight.8': ' turun paling banyak, turun ',
    'satisfaction.report.data.insight.9': ' poin.',
    'satisfaction.report.satisfaction.score': 'Skor kepuasan rata-rata',
    'satisfaction.report.improve.average.score': 'Peningkatan skor rata-rata',
    'satisfaction.report.decreased.average.score': 'Penurunan skor rata-rata',
    'satisfaction.report.average.satisfaction.score': 'poin',
    // Top 10 tiket pelanggan
    'customer.ticket.title': 'Laporan Tiket Pelanggan',
    'customer.ticket.ranking': 'Peringkat',
    'customer.ticket.customer.ticket.number': 'Nomor',
    'customer.ticket.customer.name': 'Nama pelanggan',
    // Internasionalisasi kelompok agen baru
    'agent.work.efficiency.statistics.agent.name.select.placeholder.1': 'Beberapa kelompok agen dapat dipilih untuk perbandingan data',
    // Konten kotak tips gelembung statistik efisiensi kerja
    'work.efficiency.statistics.average.resolution.time.content.text': 'Hitung rata-rata waktu resolusi semua tiket dalam periode waktu tertentu, Rata-rata waktu resolusi = SUM (Waktu Resolusi - Waktu Pembuatan) / Total Jumlah Tiket',
    'work.efficiency.statistics.data.insight.content.text': 'Lakukan analisis data mendalam berdasarkan waktu resolusi tiket, efisiensi, dan metrik kunci lainnya untuk mengidentifikasi data kritis seperti waktu resolusi terlama, terpendek, peningkatan efisiensi, dan penurunan',
    'work.efficiency.statistics.average.duration.agent.content1.text': 'Identifikasi tiga agen teratas dengan rata-rata waktu resolusi terlama dalam periode waktu tertentu',
    'work.efficiency.statistics.average.duration.agent.content2.text': 'Identifikasi tiga agen teratas dengan rata-rata waktu resolusi terpendek dalam periode waktu tertentu',
    'work.efficiency.statistics.average.duration.agent.content3.text': 'Identifikasi tiga agen teratas dengan rata-rata waktu resolusi yang paling berkurang dalam periode waktu tertentu',
    'work.efficiency.statistics.average.duration.agent.content4.text': 'Identifikasi tiga agen teratas dengan rata-rata waktu resolusi yang paling meningkat dalam periode waktu tertentu',
    'work.efficiency.statistics.average.time.resolve.tickets.text': 'Laporan tentang rata-rata waktu resolusi tiket untuk setiap agen dalam periode waktu tertentu',
    'work.efficiency.statistics.trend.average.time.resolve.tickets.text': 'Laporan tentang tren rata-rata waktu resolusi untuk setiap agen dari waktu ke waktu dalam periode waktu tertentu',
    'work.efficiency.statistics.distribution.processing.time.tickets.text': 'Hitung jumlah tiket dalam interval waktu resolusi yang berbeda (misalnya, 0-1 jam, 1-2 jam) dalam periode waktu tertentu',
    'work.efficiency.statistics.ticket.SLA.report.content.text': 'Laporan tentang kepatuhan SLA dalam periode waktu tertentu',
    'work.efficiency.statistics.select.time.tips.text': 'Rentang waktu yang dipilih harus kurang dari 24 jam.',
    // Konten kotak tips gelembung laporan kepuasan
    'satisfaction.report.average.satisfaction.score.content.text': 'Hitung rata-rata skor kepuasan semua tiket dalam periode waktu tertentu, Rata-rata skor kepuasan = Jumlah skor kepuasan tiket / Total jumlah tiket dengan skor kepuasan',
    'satisfaction.report.average.satisfaction.score.content1.text': ' Hitung rasio kepuasan balasan tiket keseluruhan dalam periode waktu tertentu, rasio kepuasan balasan = Jumlah tiket dengan skor kepuasan / Total jumlah tiket',
    'satisfaction.report.data.insight.content.text': 'Lakukan analisis data mendalam berdasarkan kepuasan pelanggan dan rasio kepuasan balasan untuk mengidentifikasi data kritis seperti skor kepuasan tertinggi, terendah, peningkatan, dan penurunan',
    'satisfaction.report.average.duration.agent.content1.text': 'Laporan tentang rata-rata skor kepuasan untuk setiap agen dalam periode waktu tertentu, Rata-rata skor kepuasan = Jumlah skor kepuasan tiket / Total jumlah tiket dengan skor kepuasan',
    'satisfaction.report.average.time.resolve.tickets.text': 'Laporan tentang tren rata-rata skor kepuasan untuk setiap agen dari waktu ke waktu dalam periode waktu tertentu',
    'satisfaction.report.distribution.processing.time.tickets.text': 'Laporan tentang rata-rata skor kepuasan berdasarkan jenis tiket untuk setiap agen dalam periode waktu tertentu',
    'satisfaction.report.channel.text': 'Laporan tentang rata-rata skor kepuasan berdasarkan saluran untuk setiap agen dalam periode waktu tertentu',
    'satisfaction.report.statistical.review.rate.agent.text': 'Hitung rasio kepuasan balasan untuk setiap agen dalam periode waktu tertentu, rasio kepuasan balasan = Jumlah tiket dengan skor kepuasan / Total jumlah tiket',
    'satisfaction.report.highest.average.satisfaction.content.text': 'Identifikasi tiga agen teratas dengan rata-rata skor kepuasan tertinggi dalam periode waktu tertentu',
    'satisfaction.report.shortest.average.satisfaction.content.text': 'Identifikasi tiga agen teratas dengan rata-rata skor kepuasan terendah dalam periode waktu tertentu',
    'satisfaction.report.average.efficiency.improvements.content.text': 'Identifikasi tiga agen teratas dengan rata-rata skor kepuasan yang paling meningkat dalam periode waktu tertentu',
    'satisfaction.report.decreases.average.efficiency.content.text': 'Identifikasi tiga agen teratas dengan rata-rata skor kepuasan yang paling menurun dalam periode waktu tertentu',
    'agent.work.efficiency.statistics.time.unit': ' (Unit: Jam)',
    'agent.work.efficiency.statistics.number.unit': ' (Unit: buah)',
    'satisfaction.report.select.agent.tips': 'Pilih maksimal lima agen!',
    'satisfaction.report.select.agent.group.tips': 'Pilih tidak lebih dari lima kelompok agen!',
    'statistics.data.details.contact.id': 'ID Kontak: ',
};
