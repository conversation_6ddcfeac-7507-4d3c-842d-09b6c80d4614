export default {
    'hotlineKeyIndicatorsConfig.title': 'Hotline KeyMetrics Dashboard Rule Config',
    'hotlineKeyIndicatorsConfig.setEmail': 'Atur notifikasi email',
    'hotlineKeyIndicatorsConfig.IVR.title': 'Volume antrian IVR',
    'hotlineKeyIndicatorsConfig.IVR.queue': 'Antrian IVR: ',
    'hotlineKeyIndicatorsConfig.IVR.queueTip': 'Antrian saat ini / Agen online saat ini',
    'hotlineKeyIndicatorsConfig.IVR.inbound': 'Waktu antrian panggilan masuk rata-rata',
    'hotlineKeyIndicatorsConfig.IVR.inboundTime': 'Saat waktu antrian panggilan masuk rata-rata lebih besar dari',
    'hotlineKeyIndicatorsConfig.IVR.inboundSecond': 'detik, tampilkan sebagai ',
    'hotlineKeyIndicatorsConfig.IVR.inboundSecondOrange': 'oranye',
    'hotlineKeyIndicatorsConfig.IVR.inboundSecondRed': 'merah',
    'hotlineKeyIndicatorsConfig.inbound.count': 'Volume panggilan masuk',
    'hotlineKeyIndicatorsConfig.inbound.countIncrease': ' Lonjakan panggilan masuk',
    'hotlineKeyIndicatorsConfig.inbound.countIncrease24H': 'Volume panggilan masuk dalam 24 jam terakhir melonjak dibandingkan 24 jam sebelumnya',
    'hotlineKeyIndicatorsConfig.inbound.countShow': '%, tampilkan sebagai',
    'hotlineKeyIndicatorsConfig.connectionRate.title': 'Tingkat koneksi',
    'hotlineKeyIndicatorsConfig.connectionRate.total': 'Tingkat koneksi total',
    'hotlineKeyIndicatorsConfig.connectionRate.inbound': 'Tingkat koneksi panggilan masuk',
    'hotlineKeyIndicatorsConfig.connectionRate.serviceInbound': 'Tingkat koneksi waktu layanan panggilan masuk',
    'hotlineKeyIndicatorsConfig.connectionRate.Outbound': 'Tingkat koneksi panggilan keluar',
    'hotlineKeyIndicatorsConfig.AWC.title': 'Waktu AWC',
    'hotlineKeyIndicatorsConfig.AWC.inbound': 'Waktu AWC panggilan masuk rata-rata',
    'hotlineKeyIndicatorsConfig.AWC.outbound': 'Waktu AWC panggilan keluar rata-rata',
    'hotlineKeyIndicatorsConfig.AWC.inboundTime': 'Saat waktu ACW panggilan masuk rata-rata lebih besar dari',
    'hotlineKeyIndicatorsConfig.AWC.outboundTime': 'Saat waktu ACW panggilan keluar rata-rata lebih besar dari',
    'hotlineKeyIndicatorsConfig.abandonment.title': 'Tingkat pengabaian',
    'hotlineKeyIndicatorsConfig.abandonment.total': 'Tingkat pengabaian total',
    'hotlineKeyIndicatorsConfig.abandonment.unreachable': 'Tingkat panggilan tak terjawab',
    'hotlineKeyIndicatorsConfig.abandonment.IVR': 'Tingkat pengabaian IVR',
    'hotlineKeyIndicatorsConfig.abandonment.abandon': 'Tingkat pengabaian antrian',
    'hotlineKeyIndicatorsConfig.abandonment.inbound': 'Tingkat panggilan masuk di luar jam kerja',
    'hotlineKeyIndicatorsConfig.satisfaction.title': 'Skor kepuasan',
    'hotlineKeyIndicatorsConfig.satisfaction.average': 'Skor kepuasan rata-rata',
    'hotlineKeyIndicatorsConfig.satisfaction.inbound': 'Skor kepuasan panggilan masuk rata-rata',
    'hotlineKeyIndicatorsConfig.satisfaction.outbound': 'Skor kepuasan panggilan keluar rata-rata',
    'hotlineKeyIndicatorsConfig.other.title': 'Konfigurasi lainnya',
    'hotlineKeyIndicatorsConfig.other.inbound': 'Tingkat transfer panggilan masuk',
    'hotlineKeyIndicatorsConfig.other.outbound': 'Tingkat transfer panggilan keluar',
    'hotlineKeyIndicatorsConfig.other.inboundHangUp': 'Tingkat penutupan panggilan agen masuk',
    'hotlineKeyIndicatorsConfig.other.outboundHangUp': 'Tingkat penutupan panggilan agen keluar',
    'hotlineKeyIndicatorsConfig.other.repeatInbound': 'Tingkat panggilan berulang',
    'hotlineKeyIndicatorsConfig.btn.save': 'Simpan',
    'hotlineKeyIndicatorsConfig.btn.cancel': 'Batal',
    'hotlineKeyIndicatorsConfig.btn.saveErr': 'Input tidak valid, silakan periksa input Anda',
    'hotlineKeyIndicatorsConfig.modal.title': 'Peringatan email metrik hotline',
    'hotlineKeyIndicatorsConfig.modal.text1': 'Saat metrik hotline',
    'hotlineKeyIndicatorsConfig.modal.tipText1': ' memicu peringatan',
    'hotlineKeyIndicatorsConfig.modal.text2': ' , pengguna platform berikut akan dikirim ',
    'hotlineKeyIndicatorsConfig.modal.tipText2': ' notifikasi email.',
    'hotlineKeyIndicatorsConfig.modal.tip': 'Catatan: Sistem akan secara otomatis memeriksa setiap jam.',
    'hotlineKeyIndicatorsConfig.modal.user': 'Beri tahu pengguna',
    'hotlineKeyIndicatorsConfig.operation.success': 'Beri tahu pengguna',
    'hotlineKeyIndicatorsConfig.operation.success.placeholder': 'Silakan pilih pengguna yang akan diberi tahu',
    'hotlineKeyIndicatorsConfig.times.text': ' kali',
};
