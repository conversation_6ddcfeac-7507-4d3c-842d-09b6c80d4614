export default {
    'worktable.notification.chat.title': '「ConnectNow」pengingat obrolan',
    'worktable.notification.chat.content': '<PERSON><PERSON> pelanggan: ',
    'worktable.notification.call.title': '「ConnectNow」pengingat panggilan masuk',
    'worktable.notification.call.content': 'Pelanggan {number} sedang memanggil, silakan jawab di stasiun kerja',
    'beta.tip': 'Fitur ini saat ini dalam tahap pengujian beta. Jika Anda memiliki persyaratan penggunaan, silakan hubungi manajer akun Anda. Terima kasih atas dukungan dan kerja samanya!',
    'ai.agent.channnel': 'Saluran',
    'ai.agent.channnel.options': '<PERSON>lih jenis saluran yang berlaku',
    'ai.agent.copy.fail': 'Agen AI kosong, penyalinan gagal!',
    'ai.agent.var': 'Manajemen variabel',
    'ai.agent.var.add': '<PERSON>bah variabel',
    'ai.agent.var.select': 'Pilih tipe',
    'ai.agent.var.setting.content.title': 'Pengingat aktif saat pelanggan tidak membalas pesan',
    'ai.agent.var.name.rules': 'Nama variabel tidak boleh dalam bahasa Cina dan memiliki panjang maksimal 200!',
    'ai.agent.verification.start': 'Setidaknya ada satu node yang terhubung ke node awal!',
    'ai.agent.verification.conditionIntent': 'Komponen pengenalan maksud hanya dapat dikaitkan dengan komponen teks dalam pertanyaan!',
    'ai.agent.verification.node': `Konten {formName} dari node {nodeName} tidak boleh kosong!`,
    'ai.agent.var.setting.content.tip': 'Pengaturan ini untuk robot untuk secara aktif mengingatkan pelanggan ketika mereka tidak merespons pesan.',
    'ai.agent.var.tabs.1': 'Variabel agen saat ini',
    'ai.agent.var.tabs.2': 'Variabel sesi saat ini',
    'ai.agent.var.tabs.3': 'Variabel global',
    'ai.agent.var.tabs.4': 'Variabel bawaan sistem',
    'ai.agent.var.table.1': 'Nama variabel',
    'ai.agent.var.table.2': 'Tipe data',
    'ai.agent.var.table.3': 'Nilai default',
    'ai.agent.var.table.4': 'Operasi',
    'ai.agent.var.table.select.option.1': 'String',
    'ai.agent.var.table.select.option.2': 'Angka',
    'ai.agent.var.table.select.option.3': 'JSON',
    'ai.agent.var.table.select.option.4': 'Array',
    'ai.agent.back.title': 'Prompt tidak tersimpan',
    'ai.agent.back.title.content': 'Agen saat ini telah dimodifikasi, apakah Anda ingin menyimpan perubahan?',
    'ai.agent.back.button.no': 'Jangan simpan',
    'ai.agent.nodes.start': 'Node awal',
    'ai.agent.nodes.header.tips.status.1': 'Belum disebarkan',
    'ai.agent.nodes.header.tips.status.2': 'Belum disimpan',
    'ai.agent.nodes.header.tips.status.3': 'Disebarkan',
    'ai.agent.nodes.header.tips.save': 'Versi terbaru telah disimpan tetapi belum disebarkan.',
    'ai.agent.nodes.header.test': 'Uji',
    'ai.agent.nodes.header.test.tips': 'Silakan simpan terlebih dahulu sebelum menguji.',
    'ai.agent.testAgent.endTest': 'Agen AI selesai',
    'ai.agent.nodes.header.shareCode': 'Masukkan kode berbagi',
    'ai.agent.nodes.header.tips.shortcut.title': 'Tips: <b>Memilih Beberapa Komponen</b>',
    'ai.agent.nodes.header.tips.shortcut.1': '• Tahan Shift dan seret untuk memilih komponen dalam area',
    'ai.agent.nodes.header.tips.shortcut.2': '• Tahan Command (Ctrl di Windows) dan klik untuk memilih komponen individual',
    'ai.agent.nodes.header.save': 'Simpan dan sebarkan',
    'ai.agent.nodes.header.save.success': 'Penyebaran berhasil',
    'ai.agent.enable.confirm.title': 'Aktifkan Agen Saat Ini',
    'ai.agent.enable.confirm.content': 'Aktifkan agen saat ini? Setelah diaktifkan, agen saat ini akan mulai berjalan.',
    'ai.agent.enable.confirm.confirm': 'Konfirmasi Aktifkan',
    'ai.agent.enable.confirm.cancel': 'Batal',
    'ai.agent.enable.success': 'Berhasil diaktifkan',
    'ai.agent.nodes.header.save.error.noIntent': 'Silakan pilih maksud sebelum menyebarkan.',
    'ai.agent.nodes.start.noDelete': 'Node awal tidak dapat dihapus!',
    'ai.agent.nodes.start.illegal': 'Silakan pilih komponen yang valid untuk operasi.',
    'ai.agent.nodes.form.name': 'Perpustakaan Komponen',
    'ai.agent.nodes.form.name.small': 'Komponen',
    'ai.agent.nodes.form.search': 'Cari',
    'ai.agent.nodes.form.message': 'Pesan Bot',
    'ai.agent.nodes.form.question': 'Ajukan Pertanyaan',
    'ai.agent.nodes.form.tools': 'Alat',
    'ai.agent.nodes.form.condition': 'Kondisi',
    'ai.agent.nodes.form.popup.p': 'Silakan masukkan nama komponen...',
    'ai.agent.nodes.form.popup.messageText.title': 'Pesan Bot',
    'ai.agent.nodes.form.popup.messageImage.radio.1': 'Unggah gambar',
    'ai.agent.nodes.form.popup.messageImage.radio.2': 'Tambahkan URL',
    'ai.agent.nodes.form.popup.messageImage.input.url': 'Masukkan URL',
    'ai.agent.nodes.form.popup.messageImage.input.url.p': 'Silakan masukkan URL',
    'ai.agent.nodes.form.popup.messageImage.input.alt': 'Deskripsi gambar',
    'ai.agent.nodes.form.popup.messageImage.input.alt.p': 'Silakan masukkan deskripsi',
    'ai.agent.nodes.form.popup.messageImage.input.note': 'Catatan: Deskripsi juga akan ditampilkan kepada pelanggan Anda.',
    'ai.agent.nodes.form.popup.messageImage.input.upload': 'Ukuran file tidak boleh melebihi 10MB.',
    'ai.agent.nodes.form.popup.messageImage.input.upload.btn': 'Klik untuk mengunggah',
    'ai.agent.nodes.form.popup.MessageVideo.radio.1': 'Unggah video',
    'ai.agent.nodes.form.popup.MessageVideo.radio.2': 'Tambah URL',
    'ai.agent.nodes.form.popup.MessageVideo.input.url': 'Masukkan URL',
    'ai.agent.nodes.form.popup.MessageVideo.input.url.p': 'Silakan masukkan URL',
    'ai.agent.nodes.form.popup.MessageVideo.input.alt': 'Deskripsi video',
    'ai.agent.nodes.form.popup.MessageVideo.input.alt.p': 'Silakan masukkan deskripsi',
    'ai.agent.nodes.form.popup.MessageVideo.input.note': 'Catatan: Deskripsi juga akan ditampilkan kepada pelanggan Anda.',
    'ai.agent.nodes.form.popup.MessageVideo.input.upload': 'Ukuran file tidak boleh melebihi 100MB.',
    'ai.agent.nodes.form.popup.MessageVideo.input.upload.btn': 'Klik untuk mengunggah',
    'ai.agent.nodes.form.popup.MessageDoc.radio.1': 'Unggah dokumen',
    'ai.agent.nodes.form.popup.MessageDoc.radio.2': 'Tambah URL',
    'ai.agent.nodes.form.popup.MessageDoc.input.url': 'Masukkan URL',
    'ai.agent.nodes.form.popup.MessageDoc.input.url.p': 'Silakan masukkan URL',
    'ai.agent.nodes.form.popup.MessageDoc.input.alt': 'Deskripsi dokumen',
    'ai.agent.nodes.form.popup.MessageDoc.input.alt.p': 'Silakan masukkan deskripsi',
    'ai.agent.nodes.form.popup.MessageDoc.input.note': 'Catatan: Deskripsi juga akan ditampilkan kepada pelanggan Anda.',
    'ai.agent.nodes.form.popup.MessageDoc.input.upload': 'Ukuran file tidak boleh melebihi 20MB.',
    'ai.agent.nodes.form.popup.MessageDoc.input.upload.btn': 'Klik untuk mengunggah',
    'ai.agent.nodes.startNode.select.title.1': 'Metode pemicu',
    'ai.agent.nodes.startNode.select.title.1.options.1': 'Pemicu maksud',
    'ai.agent.nodes.startNode.select.title.1.p': 'Silakan pilih metode pemicu',
    'ai.agent.nodes.startNode.select.title.2': 'Pilih maksud',
    'ai.agent.nodes.startNode.select.title.2.p': 'Silakan pilih maksud',
    'ai.agent.nodes.startNode.var': 'Variabel yang diteruskan',
    'ai.agent.nodes.startNode.var.table.1': 'Variabel maksud dinamis',
    'ai.agent.nodes.startNode.var.table.2': 'Variabel agen saat ini',
    'ai.agent.nodes.startNode.var.table.add.warn': 'Silakan simpan data yang sedang diedit terlebih dahulu',
    'ai.agent.nodes.ToolToAgent.title.1': 'Pesan default',
    'ai.agent.nodes.ToolToAgent.title.2': 'Aturan penugasan',
    'ai.agent.nodes.ToolToAgent.title.3': 'Tetapkan jenis tiket',
    'ai.agent.nodes.ToolToAgent.title.4': 'Tetapkan prioritas tiket',
    'ai.agent.nodes.ToolToAgent.title.5': 'Tetapkan maksud masuk',
    'ai.agent.nodes.ToolToAgent.title.6': 'Tetapkan tag pelanggan',
    'ai.agent.nodes.ToolToAgent.title.span': '(Opsional)',
    'ai.agent.nodes.ToolToAgent.radio.title.1': 'Alokasi otomatis',
    'ai.agent.nodes.ToolToAgent.radio.title.2': 'Ditentukan secara manual',
    'ai.agent.nodes.ToolToAgent.select.rule.p': 'Pilih metode alokasi',
    'ai.agent.nodes.ToolToAgent.select.agent': 'Pilih agen: ',
    'ai.agent.nodes.ToolToAgent.select.team': 'Pilih tim: ',
    'ai.agent.nodes.ToolToAgent.select.agent.p': 'Silakan pilih agen',
    'ai.agent.nodes.ToolToAgent.select.team.p': 'Silakan pilih tim',
    'ai.agent.nodes.ToolToAgent.select.type': 'Silakan pilih jenis tiket',
    'ai.agent.nodes.ToolToAgent.select.priority': 'Silakan pilih prioritas tiket',
    'ai.agent.nodes.ToolToAgent.input.default': 'Mengalihkan Anda ke agen langsung',
    'ai.agent.nodes.ToolFailure.text': 'Gagal kembali',
    'ai.agent.nodes.ToolLLM.title.1': 'Prompt',
    'ai.agent.nodes.ToolLLM.checkBox.1': 'Ditampilkan sebagai dihasilkan oleh AIGC',
    'ai.agent.nodes.ToolLLM.back.type': 'Jenis hasil',
    'ai.agent.nodes.ToolLLM.storage.var': 'Simpan dalam variabel',
    'ai.agent.nodes.ToolLLM.storage.var.p': 'Silakan pilih variabel',
    'ai.agent.nodes.ToolLLM.back.type.1': 'Teks',
    'ai.agent.nodes.ToolLLM.contentShow': 'Tampilkan hasil kembali',
    //aiagent ToolAPI
    'ai.agent.nodes.ToolAPI.title1': 'Pilih API yang akan dipanggil',
    'ai.agent.nodes.ToolAPI.title3': 'Manajemen API',
    'ai.agent.nodes.ToolAPI.title2.0': 'Catatan: Ketika ',
    'ai.agent.nodes.ToolAPI.title2.1': ' kode status HTTP',
    'ai.agent.nodes.ToolAPI.title2.2': ' adalah ',
    'ai.agent.nodes.ToolAPI.title2.3': '200',
    'ai.agent.nodes.ToolAPI.title2.4': ', sistem akan menganggap panggilan API saat ini berhasil; jika tidak, logika fallback akan dijalankan.',
    //aiagent ToolWorkHours
    'ai.agent.nodes.ToolWorkHours.title1': 'Pilih waktu kerja',
    'ai.agent.nodes.ToolWorkHours.workTime': 'Dalam jam kerja',
    'ai.agent.nodes.ToolWorkHours.notWorkTime': 'Tidak dalam jam kerja',
    //aiagent AskQuestionText
    'ai.agent.nodes.AskQuestionText.title1': 'Pemeriksaan dan penyimpanan jawaban pelanggan',
    'ai.agent.nodes.AskQuestionText.title2': 'Format variabel',
    'ai.agent.nodes.AskQuestionText.title21': 'Metode verifikasi',
    'ai.agent.nodes.AskQuestionText.title3': 'Jumlah percobaan ulang',
    'ai.agent.nodes.AskQuestionText.title4': 'Pesan kegagalan',
    'ai.agent.nodes.AskQuestionText.title5': 'Pesan kegagalan akhir',
    'ai.agent.nodes.AskQuestionText.title6': 'Validasi ekspresi reguler',
    'ai.agent.nodes.AskQuestionText.date.title1': 'Pilih Rentang Tanggal dan Waktu',
    'ai.agent.nodes.AskQuestionText.date.to': 'ke',
    'ai.agent.nodes.AskQuestionText.date.select1': 'Kustom',
    'ai.agent.nodes.AskQuestionText.date.select2': 'Termasuk Tanggal Sebelum Hari Ini',
    'ai.agent.nodes.AskQuestionText.date.select3': 'Termasuk Tanggal Setelah Hari Ini',
    'ai.agent.nodes.AskQuestionText.int.title1': 'Rentang nilai',
    'ai.agent.nodes.AskQuestionText.reg.title1': 'Silakan masukkan ekspresi reguler',
    'ai.agent.nodes.AskQuestionText.llm.title1': 'Silakan jelaskan metode pemeriksaan variabel',
    'ai.agent.nodes.AskQuestionText.SaveValue': 'Simpan dalam variabel',
    'ai.agent.nodes.AskQuestionText.None': 'Tidak ada',
    'ai.agent.nodes.AskQuestionText.Regex': 'Regex',
    'ai.agent.nodes.AskQuestionText.checkType1': 'Pemeriksaan reguler',
    'ai.agent.nodes.AskQuestionText.checkType2': 'Pemeriksaan LLM',
    //aiagent AskQuestionButton
    'ai.agent.nodes.AskQuestionButton.title1': 'Tombol',
    'ai.agent.nodes.AskQuestionButton.title2': 'Pengaturan tombol',
    'ai.agent.nodes.AskQuestionButton.type1': 'Vertikal',
    'ai.agent.nodes.AskQuestionButton.type2': 'Horizontal',
    'ai.agent.nodes.AskQuestionButton.addButton': 'Tambah tombol',
    //aiagent AskQuestionForm
    'ai.agent.nodes.AskQuestionForm.title1': 'Nama atribut',
    'ai.agent.nodes.AskQuestionForm.title2': 'Nama tampilan',
    'ai.agent.nodes.AskQuestionForm.title3': 'Petunjuk input atribut',
    'ai.agent.nodes.AskQuestionForm.title4': 'Pilih formulir atribut',
    'ai.agent.nodes.AskQuestionForm.title5': 'Nilai atribut',
    'ai.agent.nodes.AskQuestionForm.title6': 'Tambah nilai',
    'ai.agent.nodes.AskQuestionForm.attributeType.1': 'Kotak input satu baris',
    'ai.agent.nodes.AskQuestionForm.attributeType.2': 'Kotak input multi-baris',
    'ai.agent.nodes.AskQuestionForm.attributeType.3': 'Kotak dropdown pilihan tunggal',
    'ai.agent.nodes.AskQuestionForm.attributeType.4': 'Kotak dropdown pilihan ganda',
    'ai.agent.nodes.AskQuestionForm.attributeType.5': 'Tombol radio pilihan tunggal',
    'ai.agent.nodes.AskQuestionForm.attributeType.6': 'Tombol radio pilihan ganda',
    'ai.agent.nodes.AskQuestionForm.attributeType.7': 'Tanggal (ke tahun-bulan-hari)',
    'ai.agent.nodes.AskQuestionForm.attributeType.8': 'Tanggal (ke jam-menit-detik)',
    'ai.agent.nodes.AskQuestionForm.attributeType.9': 'Pemilihan rentang tanggal (ke tahun-bulan-hari)',
    'ai.agent.nodes.AskQuestionForm.attributeType.10': 'Pemilihan rentang tanggal (ke jam-menit-detik)',
    'ai.agent.nodes.AskQuestionForm.attributeType.11': 'Unggah file',
    'ai.agent.nodes.AskQuestionForm.addForm': 'Tambah variabel lain',
    'ai.agent.nodes.AskQuestionForm.addForm.button1': 'Kirim',
    'ai.agent.nodes.AskQuestionForm.addForm.button2': 'Batal',
    'ai.agent.nodes.AskQuestionForm.show.upload': 'Klik untuk mengunggah',
    'ai.agent.nodes.AskQuestionForm.show.upload.tips': 'Unggah file, hingga 5 file, setiap file hingga 20MB',
    //aiagent AskQuestionLLM
    'ai.agent.nodes.AskQuestionLLM.tip1': 'Silakan masukkan nama atribut',
    'ai.agent.nodes.AskQuestionLLM.tip2': 'Silakan masukkan kode atribut',
    'ai.agent.nodes.AskQuestionLLM.title1': 'Nama atribut',
    'ai.agent.nodes.AskQuestionLLM.title2': 'Kode atribut',
    'ai.agent.nodes.AskQuestionLLM.title3': 'Persyaratan format atribut',
    'ai.agent.nodes.AskQuestionLLM.title4': 'Simpan dalam variabel',
    //aiagent Rag
    'ai.agent.nodes.Rag.knowledgeType': 'Jenis basis pengetahuan',
    'ai.agent.nodes.Rag.selectKnowledgeTag': 'Pilih tag Pengetahuan',
    'ai.agent.nodes.Rag.ragName': 'Nama basis pengetahuan RAG',
    'ai.agent.nodes.Rag.reUserAsk': 'Tulis ulang pertanyaan pelanggan',
    'ai.agent.nodes.Rag.selectKnowledgeType': 'Pilih jenis basis pengetahuan',
    'ai.agent.nodes.Rag.faqKnoewledge': 'Basis pengetahuan FAQ',
    'ai.agent.nodes.Rag.ragKnoewledge': 'Basis pengetahuan RAG',
    'ai.agent.nodes.Rag.selectRagKnowledgeType': 'Pilih basis pengetahuan RAG',
    'ai.agent.nodes.Rag.a&qStyle': 'Mode bot',
    'ai.agent.nodes.Rag.a&qStyle1': 'Mode Profesional',
    'ai.agent.nodes.Rag.a&qStyle2': 'Mode Cerdas Emosional (Respons Lebih Lembut)',
    'ai.agent.nodes.Rag.cantUnderstand': 'Respons bot saat menghadapi pertanyaan yang tidak diketahui',
    'ai.agent.nodes.Rag.tip1': 'Dengan mengaktifkan "Mode Cerdas Emosional", Chatbot AIGC akan memberikan respons yang lebih lembut kepada pelanggan Anda.',
    'ai.agent.nodes.Rag.tip2': 'Pengaturan ini adalah respons bot saat menghadapi pertanyaan yang tidak diketahui',
    //aiagent Intent
    'ai.agent.nodes.Intent.intentName': 'Nama Maksud',
    'ai.agent.nodes.Intent.intentJudgmentBasis': 'Dasar penilaian maksud',
    'ai.agent.nodes.Intent.tip': 'Catatan: Dasar penilaian adalah penentuan AIGC apakah input pelanggan saat ini termasuk dalam maksud. Silakan isi dengan hati-hati.',
    'ai.agent.nodes.Intent.addIntent': 'Tambah maksud lain',
    'ai.agent.testAgent': 'Menguji bot',
    // 设置等待回复
    'ai.agent.waiting.reply.table.waiting.time': 'Waktu tunggu',
    'ai.agent.waiting.reply.table.reminder.language': 'Pesan pengingat',
    'ai.agent.script.add': 'Tambah pesan',
    'ai.agent.script.time.minute': ' menit ',
    'ai.agent.script.time.second': ' detik ',
    'ai.agent.script.time.tips': 'Silakan masukkan angka antara 0 dan 59',
    'ai.agent.script.settings.num.tips': 'Jumlah pengaturan tidak boleh melebihi 5',
    'ai.agent.script.settings.not.empty.tips': 'Waktu tunggu dan pesan pengingat tidak boleh kosong',
    'ai.agent.nodes.start.type.2': 'AI selamat datang secara default',
    'ai.agent.nodes.start.type.2.content': 'Ketika pelanggan berkomunikasi dengan perusahaan melalui obrolan online WEB, obrolan online APP, Shopify, dan Program Mini WeChat, Agen AI selamat datang saat ini akan dijalankan terlebih dahulu.',
    'ai.agent.nodes.start.type.3': 'Maksud Fallback Default',
    'ai.agent.nodes.start.type.3.content': `Ketika sistem tidak mencocokkan Agen AI apa pun, ia akan menjalankan Agen AI default.`,
    //aiagent ToolVariableSetting
    'ai.agent.nodes.ToolVariableSetting.title1': 'Pilih variabel',
    'ai.agent.nodes.ToolVariableSetting.title2': 'Pilih aturan penyesuaian',
    'ai.agent.nodes.ToolVariableSetting.title3': 'Masukkan konten awalan',
    'ai.agent.nodes.ToolVariableSetting.title4': 'Masukkan konten akhiran',
    'ai.agent.nodes.ToolVariableSetting.title5': 'Masukkan nilai spesifik',
    'ai.agent.nodes.ToolVariableSetting.title6': 'Masukkan rentang numerik',
    'ai.agent.nodes.ToolVariableSetting.title7': 'Masukkan nilai jumlah "1"',
    'ai.agent.nodes.ToolVariableSetting.title8': 'Masukkan nilai jumlah "2"',
    'ai.agent.nodes.ToolVariableSetting.title9': 'Masukkan nilai selisih "1"',
    'ai.agent.nodes.ToolVariableSetting.title10': 'Masukkan nilai selisih "2"',
    'ai.agent.nodes.ToolVariableSetting.title11': 'Masukkan nilai produk "1"',
    'ai.agent.nodes.ToolVariableSetting.title12': 'Masukkan nilai produk "2"',
    'ai.agent.nodes.ToolVariableSetting.title13': 'Masukkan nilai hasil bagi "1"',
    'ai.agent.nodes.ToolVariableSetting.title14': 'Masukkan nilai hasil bagi "2"',
    'ai.agent.nodes.ToolVariableSetting.title15': 'Masukkan nilai pembulatan ke atas',
    'ai.agent.nodes.ToolVariableSetting.title16': 'Masukkan nilai pembulatan ke bawah',
    'ai.agent.nodes.ToolVariableSetting.title17': 'Atribut tiket',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.num1': 'Sama dengan',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.num2': 'Penambahan dalam',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.num3': 'Pengurangan dalam',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.num4': 'Kalikan',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.num5': 'Bagi dengan',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.num6': 'Bulatkan ke atas',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.num7': 'Bulatkan ke bawah',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.str1': 'Tambah awalan',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.str2': 'Tambah akhiran',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.str3': 'Hapus spasi',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.str4': 'Tambah awalan dan Akhiran',
    'ai.agent.nodes.ToolVariableSetting.changeSelect.str5': 'Sama dengan',
    'ai.agent.nodes.ToolVariableSetting.node.title': 'Perbarui variabel',
    'ai.agent.nodes.ToolUpdateTicket.title1': 'Atribut pelanggan',
    'ai.agent.nodes.ToolUpdateTicket.title2': 'Nilai atribut pelanggan',
    'ai.agent.nodes.ToolUpdateTicket.title3': 'Modifikasi atribut lain',
    'ai.agent.nodes.ToolUpdateTicket.title4': 'Nilai atribut',
    'ai.agent.nodes.ToolUpdateTicket.title5': 'Atribut tiket',
    'ai.agent.nodes.ToolUpdateTicket.title6': 'Aturan nilai atribut: resolved(diselesaikan) terminated(dihentikan)',
    'ai.agent.nodes.ToolUpdateTicket.title7': 'Aturan nilai atribut: (P5) dampak rendah (P4) dampak menengah (P3) dampak parah (P2) pemrosesan darurat (P1) crash layanan',
    'ai.agent.nodes.ToolUpdateTicket.title8': 'Aturan nilai atribut: 1 (Laki-laki), 2 (Perempuan), 3 (Lainnya)',
    'ai.agent.nodes.ToolUpdateTicket.title9': 'Aturan nilai atribut: 1 (pengguna VIP), 2 (pengguna biasa)',
    'ai.agent.nodes.ToolUpdateTicket.title10': 'Silakan masukkan format tanggal standar, mis., 1979-04-01',
    'ai.agent.nodes.ToolUpdateTicket.title1005': 'Silakan masukkan nilai opsi (dapat diperoleh dari definisi atribut diperluas tiket), untuk ditambahkan di "Tab Opsi"; pengguna hanya dapat memilih satu opsi',
    'ai.agent.nodes.ToolUpdateTicket.title1006': 'Silakan masukkan nilai opsi (dapat diperoleh dari definisi atribut diperluas tiket), dipisahkan dengan koma (,); pengguna dapat memilih beberapa opsi',
    'ai.agent.nodes.ToolUpdateTicket.title1007': 'Aturan nilai atribut: true (diaktifkan) / false (dinonaktifkan)',
    'ai.agent.nodes.ToolUpdateTicket.title1008': 'Silakan masukkan format waktu standar, mis., 2025-04-03 00:00:00',
    'ai.agent.nodes.ConditionCheck.title1': 'Kondisi',
    'ai.agent.nodes.ConditionCheck.title2': 'Lainnya',
    'ai.agent.nodes.ConditionCheck.title3': 'Gagal',
    'ai.agent.nodes.ConditionCheck.title4': 'Jika',
    'ai.agent.nodes.ConditionCheck.title5': 'Adalah',
    'ai.agent.nodes.ConditionCheck.title6': 'Berisi',
    'ai.agent.nodes.ConditionCheck.title7': 'Kosong',
    'ai.agent.nodes.ConditionCheck.title8': 'Tidak kosong',
    'ai.agent.nodes.ConditionCheck.title9': 'Input pelanggan',
    'ai.agent.nodes.ConditionCheck.title10': 'Tambah kondisi',
    'ai.agent.nodes.ConditionCheck.title11': 'Lebih besar dari',
    'ai.agent.nodes.ConditionCheck.title12': 'Kurang dari',
    'ai.agent.nodes.AskQuestionLLM.addForm': 'Tambah atribut',
    'ai.agent.nodes.ToolSetCustomerTag.title1': 'Pilih tag',
    // MessageHotIssue
    'ai.agent.nodes.MessageHotIssue.title1': 'Metode konfigurasi',
    'ai.agent.nodes.MessageHotIssue.title1.type1': 'Edit manual',
    'ai.agent.nodes.MessageHotIssue.title1.type2': 'Rekomendasi otomatis',
    'ai.agent.nodes.MessageHotIssue.title2': 'Format tampilan',
    'ai.agent.nodes.MessageHotIssue.title2.type1': 'Horizontal',
    'ai.agent.nodes.MessageHotIssue.title2.type2': 'Vertikal',
    'ai.agent.nodes.MessageHotIssue.title3': 'Pilih bahasa awal',
    'ai.agent.nodes.MessageHotIssue.title4': 'Nama kategori',
    'ai.agent.nodes.MessageHotIssue.title4.tip': 'Masukkan nama kategori',
    'ai.agent.nodes.MessageHotIssue.title5': 'Pertanyaan',
    'ai.agent.nodes.MessageHotIssue.title5.tip': 'Masukkan pertanyaan',
    'ai.agent.nodes.MessageHotIssue.title6': 'Tambah pertanyaan',
    'ai.agent.nodes.MessageHotIssue.title7': 'Tambah kategori',
    'ai.agent.nodes.MessageHotIssue.title8': 'Pertanyaan teratas',
    'ai.agent.nodes.MessageHotIssue.title9': 'Rekomendasi otomatis',
    'ai.agent.nodes.MessageHotIssue.title10': 'Terjemahan cerdas',
    'ai.agent.nodes.MessageHotIssue.title11': 'Bahasa target',
    'ai.agent.nodes.MessageHotIssue.title12': 'Aturan rekomendasi FAQ otomatis',
    'ai.agent.nodes.MessageHotIssue.title12.tip1': 'Secara otomatis menampilkan',
    'ai.agent.nodes.MessageHotIssue.title12.tip2': "hari terakhir teratas",
    'ai.agent.nodes.MessageHotIssue.title12.tip3': 'FAQ',
    'ai.agent.nodes.MessageHotIssue.title12.remark': 'Catatan: Jika FAQ memiliki beberapa variasi pertanyaan, hanya yang pertama yang ditampilkan',
    'ai.agent.nodes.AskQuestionCard.title1': 'Silakan pilih pesanan Anda',
    'ai.agent.nodes.AskQuestionCard.title2': 'Tata letak kartu',
    'ai.agent.nodes.AskQuestionCard.title3': 'Ini adalah contoh judul produk. Anda dapat menjelaskan fitur utama produk di sini',
    'ai.agent.nodes.AskQuestionCard.title4': '$0.00',
    'ai.agent.nodes.AskQuestionCard.title5': 'Dikirim',
    'ai.agent.nodes.AskQuestionCard.title6': 'Carousel',
    'ai.agent.nodes.AskQuestionCard.title7': 'Daftar',
    'ai.agent.nodes.AskQuestionCard.title8': 'Data kartu',
    'ai.agent.nodes.AskQuestionCard.title9': 'URL gambar',
    'ai.agent.nodes.AskQuestionCard.title10': 'Judul produk',
    'ai.agent.nodes.AskQuestionCard.title11': 'Harga produk (Opsional)',
    'ai.agent.nodes.AskQuestionCard.title12': 'Kuantitas produk (Opsional)',
    'ai.agent.nodes.AskQuestionCard.title13': 'Status pesanan (Opsional)',
    'ai.agent.nodes.AskQuestionCard.title14': 'Catatan: Ini adalah variabel json',
    'ai.agent.nodes.AskQuestionCard.title15': 'Format variabel JSON:',
    'ai.agent.nodes.AskQuestionCard.tip1': 'Ambil data dengan memanggil API dan simpan dalam variabel JSON untuk digunakan sebagai daftar untuk data kartu. Petakan properti setiap objek JSON dalam daftar ke bidang yang sesuai di bawah.',
    'ai.agent.nodes.ToolDelay.title1': 'Tunda selama x detik',
    'ai.agent.nodes.header.save.error.formCancle': 'Pastikan node batal formulir terhubung',
    'ai.agent.exit.confirm': 'Anda memiliki perubahan yang belum disimpan. Apakah Anda yakin ingin keluar?',
};
